"use client"

import { useState } from "react"
import { createClient } from "@/lib/supabase/client"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { useRouter } from "next/navigation"

interface CashRequest {
  id: string
  request_number: string
  amount: number
  purpose: string
  description: string | null
  status: string
  created_at: string
  submitted_at: string | null
  cashier: {
    full_name: string
    email: string
  }
}

interface User {
  id: string
  role: string
  full_name: string
}

interface RequestsListClientProps {
  requests: CashRequest[]
  currentUser: User
}

export function RequestsListClient({ requests, currentUser }: RequestsListClientProps) {
  const [selectedRequest, setSelectedRequest] = useState<CashRequest | null>(null)
  const [isActionDialogOpen, setIsActionDialogOpen] = useState(false)
  const [actionType, setActionType] = useState<"approve" | "reject" | null>(null)
  const [comments, setComments] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const router = useRouter()
  const supabase = createClient()

  const getStatusDisplayName = (status: string): string => {
    const statusNames = {
      draft: "مسودة",
      submitted: "مقدم للموافقة",
      director_approved: "موافقة المدير",
      accountant_approved: "موافقة المحاسب",
      completed: "مكتمل",
      rejected: "مرفوض",
    }
    return statusNames[status as keyof typeof statusNames] || status
  }

  const getStatusBadgeColor = (status: string): string => {
    const colors = {
      draft: "bg-gray-100 text-gray-800",
      submitted: "bg-yellow-100 text-yellow-800",
      director_approved: "bg-blue-100 text-blue-800",
      accountant_approved: "bg-green-100 text-green-800",
      completed: "bg-green-100 text-green-800",
      rejected: "bg-red-100 text-red-800",
    }
    return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800"
  }

  const canApprove = (request: CashRequest): boolean => {
    if (currentUser.role === "director" || currentUser.role === "deputy_director") {
      return request.status === "submitted"
    }
    if (currentUser.role === "accountant") {
      return request.status === "director_approved"
    }
    return false
  }

  const canReject = (request: CashRequest): boolean => {
    return (
      ((currentUser.role === "director" || currentUser.role === "deputy_director") &&
        (request.status === "submitted" || request.status === "director_approved")) ||
      (currentUser.role === "accountant" && request.status === "director_approved")
    )
  }

  const handleAction = async () => {
    if (!selectedRequest || !actionType) return

    setIsLoading(true)
    setError(null)

    try {
      let newStatus = selectedRequest.status
      let actionName = ""

      if (actionType === "approve") {
        if (currentUser.role === "director" || currentUser.role === "deputy_director") {
          newStatus = "director_approved"
          actionName = "approved"
        } else if (currentUser.role === "accountant") {
          newStatus = "accountant_approved"
          actionName = "approved"
        }
      } else if (actionType === "reject") {
        newStatus = "rejected"
        actionName = "rejected"
      }

      // Update request status
      const updateData: any = {
        status: newStatus,
        updated_at: new Date().toISOString(),
      }

      if (actionType === "approve") {
        if (currentUser.role === "director" || currentUser.role === "deputy_director") {
          updateData.director_approved_at = new Date().toISOString()
        } else if (currentUser.role === "accountant") {
          updateData.accountant_approved_at = new Date().toISOString()
        }
      } else if (actionType === "reject") {
        updateData.rejected_at = new Date().toISOString()
        updateData.rejection_reason = comments.trim() || null
      }

      const { error: updateError } = await supabase
        .from("cash_requests")
        .update(updateData)
        .eq("id", selectedRequest.id)

      if (updateError) throw updateError

      // Add workflow history entry
      await supabase.from("workflow_history").insert([
        {
          cash_request_id: selectedRequest.id,
          user_id: currentUser.id,
          action: actionName,
          from_status: selectedRequest.status,
          to_status: newStatus,
          comments: comments.trim() || null,
        },
      ])

      setIsActionDialogOpen(false)
      setSelectedRequest(null)
      setActionType(null)
      setComments("")
      router.refresh()
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : "حدث خطأ أثناء تنفيذ العملية")
    } finally {
      setIsLoading(false)
    }
  }

  const openActionDialog = (request: CashRequest, action: "approve" | "reject") => {
    setSelectedRequest(request)
    setActionType(action)
    setComments("")
    setError(null)
    setIsActionDialogOpen(true)
  }

  return (
    <div className="space-y-6">
      {error && <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">{error}</div>}

      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">قائمة الطلبات</h2>
          <p className="text-sm text-gray-600">إجمالي الطلبات: {requests.length}</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>طلبات النقدية</CardTitle>
          <CardDescription>قائمة جميع طلبات النقدية</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-right">رقم الطلب</TableHead>
                <TableHead className="text-right">مقدم الطلب</TableHead>
                <TableHead className="text-right">المبلغ</TableHead>
                <TableHead className="text-right">الغرض</TableHead>
                <TableHead className="text-right">الحالة</TableHead>
                <TableHead className="text-right">تاريخ الإنشاء</TableHead>
                <TableHead className="text-right">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {requests.map((request) => (
                <TableRow key={request.id}>
                  <TableCell className="font-medium text-right">
                    <a href={`/requests/${request.id}`} className="text-blue-600 hover:text-blue-800 hover:underline">
                      {request.request_number}
                    </a>
                  </TableCell>
                  <TableCell className="text-right">{request.cashier.full_name}</TableCell>
                  <TableCell className="text-right">{request.amount.toLocaleString("ar-MA")} درهم</TableCell>
                  <TableCell className="text-right">{request.purpose}</TableCell>
                  <TableCell className="text-right">
                    <Badge className={getStatusBadgeColor(request.status)}>
                      {getStatusDisplayName(request.status)}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    {new Date(request.created_at).toLocaleDateString("ar-MA")}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex space-x-2 space-x-reverse">
                      <Button size="sm" variant="outline" onClick={() => router.push(`/requests/${request.id}`)}>
                        عرض
                      </Button>
                      {canApprove(request) && (
                        <Button
                          size="sm"
                          className="bg-green-600 hover:bg-green-700"
                          onClick={() => openActionDialog(request, "approve")}
                        >
                          موافقة
                        </Button>
                      )}
                      {canReject(request) && (
                        <Button size="sm" variant="destructive" onClick={() => openActionDialog(request, "reject")}>
                          رفض
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Action Dialog */}
      <Dialog open={isActionDialogOpen} onOpenChange={setIsActionDialogOpen}>
        <DialogContent className="sm:max-w-md" dir="rtl">
          <DialogHeader>
            <DialogTitle>{actionType === "approve" ? "موافقة على الطلب" : "رفض الطلب"}</DialogTitle>
            <DialogDescription>{selectedRequest && `طلب رقم: ${selectedRequest.request_number}`}</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="comments">{actionType === "approve" ? "ملاحظات (اختياري)" : "سبب الرفض"}</Label>
              <Textarea
                id="comments"
                rows={3}
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                className="text-right"
                placeholder={actionType === "approve" ? "أدخل أي ملاحظات إضافية..." : "أدخل سبب رفض الطلب..."}
                required={actionType === "reject"}
              />
            </div>
            <div className="flex justify-end space-x-2 space-x-reverse">
              <Button type="button" variant="outline" onClick={() => setIsActionDialogOpen(false)}>
                إلغاء
              </Button>
              <Button
                type="button"
                onClick={handleAction}
                disabled={isLoading || (actionType === "reject" && !comments.trim())}
                className={actionType === "approve" ? "bg-green-600 hover:bg-green-700" : ""}
                variant={actionType === "reject" ? "destructive" : "default"}
              >
                {isLoading ? "جاري التنفيذ..." : actionType === "approve" ? "موافقة" : "رفض"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
