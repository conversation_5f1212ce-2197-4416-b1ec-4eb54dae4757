"use client"

import type { User } from "@supabase/supabase-js"
import { usePathname } from "next/navigation"
import Link from "next/link"
import { useState } from "react"
import { useIsMobile } from "@/hooks/use-mobile"
import { Button } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"

interface NavigationProps {
  user: User
  userProfile: {
    id: string
    full_name: string
    role: string
    email: string
    is_active: boolean
  }
}

export function Navigation({ user, userProfile }: NavigationProps) {
  const pathname = usePathname()
  const isMobile = useIsMobile()
  const [isOpen, setIsOpen] = useState(false)

  const navigationItems = [
    {
      name: "الصفحة الرئيسية",
      href: "/dashboard",
      icon: "🏠",
      roles: ["cashier", "director", "deputy_director", "accountant"],
    },
    {
      name: "الطلبات",
      href: "/requests",
      icon: "📋",
      roles: ["cashier", "director", "deputy_director", "accountant"],
    },
    {
      name: "إنشاء طلب جديد",
      href: "/requests/new",
      icon: "➕",
      roles: ["cashier"],
    },
    {
      name: "العمليات المالية",
      href: "/operations",
      icon: "💰",
      roles: ["cashier"],
    },
    {
      name: "إدارة المستخدمين",
      href: "/users",
      icon: "👥",
      roles: ["director", "deputy_director"],
    },
  ]

  const filteredItems = navigationItems.filter((item) => item.roles.includes(userProfile.role))

  const NavigationItems = ({ onItemClick }: { onItemClick?: () => void }) => (
    <>
      {filteredItems.map((item) => (
        <Link
          key={item.href}
          href={item.href}
          onClick={onItemClick}
          className={`flex items-center space-x-2 space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-colors ${
            pathname === item.href
              ? "bg-blue-100 text-blue-700"
              : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
          }`}
        >
          <span>{item.icon}</span>
          <span>{item.name}</span>
        </Link>
      ))}
    </>
  )

  if (isMobile) {
    return (
      <div className="bg-white shadow-sm border-b">
        <div className="px-4 py-3">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-3 space-x-reverse">
              <Sheet open={isOpen} onOpenChange={setIsOpen}>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="sm" className="p-2">
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-80">
                  <div className="flex flex-col space-y-4 mt-6">
                    <div className="border-b pb-4">
                      <h2 className="text-lg font-semibold text-gray-900">نظام إدارة طلبات النقدية</h2>
                      <p className="text-sm text-gray-600 mt-1">
                        {userProfile.full_name} ({getRoleDisplayName(userProfile.role)})
                      </p>
                    </div>
                    <nav className="flex flex-col space-y-2">
                      <NavigationItems onItemClick={() => setIsOpen(false)} />
                    </nav>
                    <div className="border-t pt-4">
                      <form action="/auth/logout" method="post">
                        <button
                          type="submit"
                          className="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm"
                        >
                          تسجيل الخروج
                        </button>
                      </form>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
              <div>
                <h1 className="text-lg font-bold text-gray-900">نظام النقدية</h1>
              </div>
            </div>
            <div className="text-xs text-gray-600">
              {userProfile.full_name}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">نظام إدارة طلبات النقدية</h1>
            <p className="text-sm text-gray-600">
              مرحباً، {userProfile.full_name} ({getRoleDisplayName(userProfile.role)})
            </p>
          </div>
          <div className="flex space-x-4 space-x-reverse">
            <form action="/auth/logout" method="post">
              <button type="submit" className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm">
                تسجيل الخروج
              </button>
            </form>
          </div>
        </div>

        {/* Navigation Menu */}
        <nav className="flex space-x-8 space-x-reverse border-t pt-4 pb-2">
          <NavigationItems />
        </nav>
      </div>
    </div>
  )
}

function getRoleDisplayName(role: string): string {
  const roleNames = {
    cashier: "أمين الصندوق",
    director: "المدير العام",
    deputy_director: "نائب المدير",
    accountant: "المحاسب المالي",
  }
  return roleNames[role as keyof typeof roleNames] || role
}
