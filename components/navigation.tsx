"use client"

import type { User } from "@supabase/supabase-js"
import { usePathname } from "next/navigation"
import Link from "next/link"

interface NavigationProps {
  user: User
  userProfile: {
    id: string
    full_name: string
    role: string
    email: string
    is_active: boolean
  }
}

export function Navigation({ user, userProfile }: NavigationProps) {
  const pathname = usePathname()

  const navigationItems = [
    {
      name: "الصفحة الرئيسية",
      href: "/dashboard",
      icon: "🏠",
      roles: ["cashier", "director", "deputy_director", "accountant"],
    },
    {
      name: "الطلبات",
      href: "/requests",
      icon: "📋",
      roles: ["cashier", "director", "deputy_director", "accountant"],
    },
    {
      name: "إنشاء طلب جديد",
      href: "/requests/new",
      icon: "➕",
      roles: ["cashier"],
    },
    {
      name: "العمليات المالية",
      href: "/operations",
      icon: "💰",
      roles: ["cashier"],
    },
    {
      name: "إدارة المستخدمين",
      href: "/users",
      icon: "👥",
      roles: ["director", "deputy_director"],
    },
  ]

  const filteredItems = navigationItems.filter((item) => item.roles.includes(userProfile.role))

  return (
    <div className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">نظام إدارة طلبات النقدية</h1>
            <p className="text-sm text-gray-600">
              مرحباً، {userProfile.full_name} ({getRoleDisplayName(userProfile.role)})
            </p>
          </div>
          <div className="flex space-x-4 space-x-reverse">
            <form action="/auth/logout" method="post">
              <button type="submit" className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm">
                تسجيل الخروج
              </button>
            </form>
          </div>
        </div>

        {/* Navigation Menu */}
        <nav className="flex space-x-8 space-x-reverse border-t pt-4 pb-2">
          {filteredItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={`flex items-center space-x-2 space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                pathname === item.href
                  ? "bg-blue-100 text-blue-700"
                  : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              }`}
            >
              <span>{item.icon}</span>
              <span>{item.name}</span>
            </Link>
          ))}
        </nav>
      </div>
    </div>
  )
}

function getRoleDisplayName(role: string): string {
  const roleNames = {
    cashier: "أمين الصندوق",
    director: "المدير العام",
    deputy_director: "نائب المدير",
    accountant: "المحاسب المالي",
  }
  return roleNames[role as keyof typeof roleNames] || role
}
