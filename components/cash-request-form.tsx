"use client"

import type React from "react"

import { useState } from "react"
import { createClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"

interface CashRequestFormProps {
  userId: string
}

export function CashRequestForm({ userId }: CashRequestFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    amount: "",
    purpose: "",
    description: "",
  })

  const router = useRouter()
  const supabase = createClient()

  const handleSubmit = async (e: React.FormEvent, isDraft = false) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)

    try {
      const amount = Number.parseFloat(formData.amount)
      if (isNaN(amount) || amount <= 0) {
        throw new Error("يجب أن يكون المبلغ رقماً صحيحاً أكبر من صفر")
      }

      if (!formData.purpose.trim()) {
        throw new Error("يجب تحديد الغرض من الطلب")
      }

      const requestData = {
        cashier_id: userId,
        amount: amount,
        purpose: formData.purpose.trim(),
        description: formData.description.trim() || null,
        status: isDraft ? "draft" : "submitted",
        submitted_at: isDraft ? null : new Date().toISOString(),
      }

      const { data, error } = await supabase.from("cash_requests").insert([requestData]).select().single()

      if (error) throw error

      // Add workflow history entry
      await supabase.from("workflow_history").insert([
        {
          cash_request_id: data.id,
          user_id: userId,
          action: isDraft ? "created" : "submitted",
          from_status: null,
          to_status: isDraft ? "draft" : "submitted",
          comments: isDraft ? "تم إنشاء الطلب كمسودة" : "تم تقديم الطلب للموافقة",
        },
      ])

      router.push("/requests")
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : "حدث خطأ أثناء حفظ الطلب")
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>بيانات الطلب</CardTitle>
        <CardDescription>أدخل تفاصيل طلب النقدية</CardDescription>
      </CardHeader>
      <CardContent>
        {error && <div className="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">{error}</div>}

        <form className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="amount" className="text-right">
              المبلغ المطلوب *
            </Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              min="0"
              required
              value={formData.amount}
              onChange={(e) => handleInputChange("amount", e.target.value)}
              className="text-right"
              placeholder="0.00"
            />
            <p className="text-sm text-gray-500 text-right">أدخل المبلغ بالدرهم المغربي</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="purpose" className="text-right">
              الغرض من الطلب *
            </Label>
            <Input
              id="purpose"
              type="text"
              required
              value={formData.purpose}
              onChange={(e) => handleInputChange("purpose", e.target.value)}
              className="text-right"
              placeholder="مثال: شراء مستلزمات مكتبية"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="text-right">
              وصف تفصيلي (اختياري)
            </Label>
            <Textarea
              id="description"
              rows={4}
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              className="text-right"
              placeholder="أدخل تفاصيل إضافية عن الطلب..."
            />
          </div>

          <div className="flex justify-end space-x-4 space-x-reverse pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={(e) => handleSubmit(e, true)}
              disabled={isLoading}
              className="min-w-[120px]"
            >
              {isLoading ? "جاري الحفظ..." : "حفظ كمسودة"}
            </Button>
            <Button
              type="button"
              onClick={(e) => handleSubmit(e, false)}
              disabled={isLoading}
              className="min-w-[120px] bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? "جاري التقديم..." : "تقديم الطلب"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
