"use client"

import type React from "react"

import { useState } from "react"
import { createClient } from "@/lib/supabase/client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useRouter } from "next/navigation"

interface CashRequest {
  id: string
  request_number: string
  amount: number
  purpose: string
  description: string | null
  accountant_approved_at: string
  operations: Operation[]
}

interface Operation {
  id: string
  operation_type: string
  amount: number
  operation_date: string
  notes: string | null
  cash_request: {
    request_number: string
    purpose: string
    amount: number
  }
}

interface User {
  id: string
  role: string
  full_name: string
}

interface OperationsClientProps {
  approvedRequests: CashRequest[]
  allOperations: Operation[]
  currentUser: User
}

export function OperationsClient({ approvedRequests, allOperations, currentUser }: OperationsClientProps) {
  const [selectedRequest, setSelectedRequest] = useState<CashRequest | null>(null)
  const [isOperationDialogOpen, setIsOperationDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const [operationData, setOperationData] = useState({
    operation_type: "receive",
    amount: "",
    notes: "",
  })

  const router = useRouter()
  const supabase = createClient()

  const getOperationTypeDisplayName = (type: string): string => {
    return type === "receive" ? "استلام" : "تسليم"
  }

  const getOperationTypeBadgeColor = (type: string): string => {
    return type === "receive" ? "bg-green-100 text-green-800" : "bg-blue-100 text-blue-800"
  }

  const getTotalOperations = (request: CashRequest): { received: number; given: number } => {
    const received = request.operations
      .filter((op) => op.operation_type === "receive")
      .reduce((sum, op) => sum + Number(op.amount), 0)
    const given = request.operations
      .filter((op) => op.operation_type === "give")
      .reduce((sum, op) => sum + Number(op.amount), 0)
    return { received, given }
  }

  const handleOperation = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedRequest) return

    setIsLoading(true)
    setError(null)

    try {
      const amount = Number.parseFloat(operationData.amount)
      if (isNaN(amount) || amount <= 0) {
        throw new Error("يجب أن يكون المبلغ رقماً صحيحاً أكبر من صفر")
      }

      // Check if operation amount doesn't exceed request amount
      const { received, given } = getTotalOperations(selectedRequest)
      const currentBalance = received - given

      if (operationData.operation_type === "give" && amount > currentBalance) {
        throw new Error("لا يمكن تسليم مبلغ أكبر من الرصيد المتاح")
      }

      // Insert operation
      const { error: insertError } = await supabase.from("operations").insert([
        {
          cash_request_id: selectedRequest.id,
          operation_type: operationData.operation_type,
          amount: amount,
          performed_by: currentUser.id,
          notes: operationData.notes.trim() || null,
          operation_date: new Date().toISOString(),
        },
      ])

      if (insertError) throw insertError

      // Check if request is fully completed
      const newReceived = operationData.operation_type === "receive" ? received + amount : received
      const newGiven = operationData.operation_type === "give" ? given + amount : given

      if (newGiven >= selectedRequest.amount) {
        // Mark request as completed
        await supabase
          .from("cash_requests")
          .update({
            status: "completed",
            completed_at: new Date().toISOString(),
          })
          .eq("id", selectedRequest.id)

        // Add workflow history
        await supabase.from("workflow_history").insert([
          {
            cash_request_id: selectedRequest.id,
            user_id: currentUser.id,
            action: "completed",
            from_status: "accountant_approved",
            to_status: "completed",
            comments: "تم إكمال جميع العمليات المالية للطلب",
          },
        ])
      }

      setIsOperationDialogOpen(false)
      setSelectedRequest(null)
      setOperationData({ operation_type: "receive", amount: "", notes: "" })
      router.refresh()
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : "حدث خطأ أثناء تسجيل العملية")
    } finally {
      setIsLoading(false)
    }
  }

  const openOperationDialog = (request: CashRequest) => {
    setSelectedRequest(request)
    setOperationData({ operation_type: "receive", amount: "", notes: "" })
    setError(null)
    setIsOperationDialogOpen(true)
  }

  return (
    <div className="space-y-8">
      {error && <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">{error}</div>}

      {/* Approved Requests Needing Operations */}
      <Card>
        <CardHeader>
          <CardTitle>الطلبات المعتمدة</CardTitle>
          <CardDescription>الطلبات التي تحتاج لعمليات مالية</CardDescription>
        </CardHeader>
        <CardContent>
          {approvedRequests.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-right">رقم الطلب</TableHead>
                  <TableHead className="text-right">الغرض</TableHead>
                  <TableHead className="text-right">المبلغ المطلوب</TableHead>
                  <TableHead className="text-right">تم استلامه</TableHead>
                  <TableHead className="text-right">تم تسليمه</TableHead>
                  <TableHead className="text-right">الرصيد</TableHead>
                  <TableHead className="text-right">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {approvedRequests.map((request) => {
                  const { received, given } = getTotalOperations(request)
                  const balance = received - given
                  return (
                    <TableRow key={request.id}>
                      <TableCell className="font-medium text-right">{request.request_number}</TableCell>
                      <TableCell className="text-right">{request.purpose}</TableCell>
                      <TableCell className="text-right">{request.amount.toLocaleString("ar-MA")} درهم</TableCell>
                      <TableCell className="text-right text-green-600">
                        {received.toLocaleString("ar-MA")} درهم
                      </TableCell>
                      <TableCell className="text-right text-blue-600">{given.toLocaleString("ar-MA")} درهم</TableCell>
                      <TableCell className="text-right font-semibold">{balance.toLocaleString("ar-MA")} درهم</TableCell>
                      <TableCell className="text-right">
                        <Button size="sm" onClick={() => openOperationDialog(request)}>
                          إضافة عملية
                        </Button>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          ) : (
            <p className="text-center text-gray-500 py-8">لا توجد طلبات معتمدة تحتاج لعمليات مالية</p>
          )}
        </CardContent>
      </Card>

      {/* Operations History */}
      <Card>
        <CardHeader>
          <CardTitle>سجل العمليات</CardTitle>
          <CardDescription>جميع العمليات المالية التي قمت بها</CardDescription>
        </CardHeader>
        <CardContent>
          {allOperations.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-right">رقم الطلب</TableHead>
                  <TableHead className="text-right">نوع العملية</TableHead>
                  <TableHead className="text-right">المبلغ</TableHead>
                  <TableHead className="text-right">تاريخ العملية</TableHead>
                  <TableHead className="text-right">ملاحظات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {allOperations.map((operation) => (
                  <TableRow key={operation.id}>
                    <TableCell className="font-medium text-right">{operation.cash_request.request_number}</TableCell>
                    <TableCell className="text-right">
                      <Badge className={getOperationTypeBadgeColor(operation.operation_type)}>
                        {getOperationTypeDisplayName(operation.operation_type)}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      {Number(operation.amount).toLocaleString("ar-MA")} درهم
                    </TableCell>
                    <TableCell className="text-right">
                      {new Date(operation.operation_date).toLocaleString("ar-MA")}
                    </TableCell>
                    <TableCell className="text-right">{operation.notes || "-"}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <p className="text-center text-gray-500 py-8">لا توجد عمليات مالية حتى الآن</p>
          )}
        </CardContent>
      </Card>

      {/* Operation Dialog */}
      <Dialog open={isOperationDialogOpen} onOpenChange={setIsOperationDialogOpen}>
        <DialogContent className="sm:max-w-md" dir="rtl">
          <DialogHeader>
            <DialogTitle>إضافة عملية مالية</DialogTitle>
            <DialogDescription>{selectedRequest && `طلب رقم: ${selectedRequest.request_number}`}</DialogDescription>
          </DialogHeader>
          <form onSubmit={handleOperation} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="operation_type">نوع العملية</Label>
              <Select
                value={operationData.operation_type}
                onValueChange={(value) => setOperationData({ ...operationData, operation_type: value })}
              >
                <SelectTrigger className="text-right">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="receive">استلام نقدية</SelectItem>
                  <SelectItem value="give">تسليم نقدية</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">المبلغ</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="0"
                required
                value={operationData.amount}
                onChange={(e) => setOperationData({ ...operationData, amount: e.target.value })}
                className="text-right"
                placeholder="0.00"
              />
              {selectedRequest && (
                <p className="text-sm text-gray-500 text-right">
                  المبلغ المطلوب: {selectedRequest.amount.toLocaleString("ar-MA")} درهم
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">ملاحظات (اختياري)</Label>
              <Textarea
                id="notes"
                rows={3}
                value={operationData.notes}
                onChange={(e) => setOperationData({ ...operationData, notes: e.target.value })}
                className="text-right"
                placeholder="أدخل أي ملاحظات إضافية..."
              />
            </div>

            <div className="flex justify-end space-x-2 space-x-reverse">
              <Button type="button" variant="outline" onClick={() => setIsOperationDialogOpen(false)}>
                إلغاء
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "جاري التسجيل..." : "تسجيل العملية"}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
