"use client"

import { useState } from "react"
import { createClient } from "@/lib/supabase/client"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { useRouter } from "next/navigation"

interface CashRequest {
  id: string
  request_number: string
  amount: number
  purpose: string
  description: string | null
  status: string
  created_at: string
  submitted_at: string | null
  director_approved_at: string | null
  accountant_approved_at: string | null
  completed_at: string | null
  rejected_at: string | null
  rejection_reason: string | null
  cashier: {
    full_name: string
    email: string
    role: string
  }
}

interface WorkflowHistoryItem {
  id: string
  action: string
  from_status: string | null
  to_status: string
  comments: string | null
  created_at: string
  user: {
    full_name: string
    role: string
  }
}

interface User {
  id: string
  role: string
  full_name: string
}

interface RequestDetailsClientProps {
  request: CashRequest
  workflowHistory: WorkflowHistoryItem[]
  currentUser: User
}

export function RequestDetailsClient({ request, workflowHistory, currentUser }: RequestDetailsClientProps) {
  const [isActionDialogOpen, setIsActionDialogOpen] = useState(false)
  const [actionType, setActionType] = useState<"approve" | "reject" | "complete" | null>(null)
  const [comments, setComments] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const router = useRouter()
  const supabase = createClient()

  const getStatusDisplayName = (status: string): string => {
    const statusNames = {
      draft: "مسودة",
      submitted: "مقدم للموافقة",
      director_approved: "موافقة المدير",
      accountant_approved: "موافقة المحاسب",
      completed: "مكتمل",
      rejected: "مرفوض",
    }
    return statusNames[status as keyof typeof statusNames] || status
  }

  const getStatusBadgeColor = (status: string): string => {
    const colors = {
      draft: "bg-gray-100 text-gray-800",
      submitted: "bg-yellow-100 text-yellow-800",
      director_approved: "bg-blue-100 text-blue-800",
      accountant_approved: "bg-green-100 text-green-800",
      completed: "bg-green-100 text-green-800",
      rejected: "bg-red-100 text-red-800",
    }
    return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800"
  }

  const getRoleDisplayName = (role: string): string => {
    const roleNames = {
      cashier: "أمين الصندوق",
      director: "المدير",
      deputy_director: "نائب المدير",
      accountant: "المحاسب",
    }
    return roleNames[role as keyof typeof roleNames] || role
  }

  const getActionDisplayName = (action: string): string => {
    const actionNames = {
      created: "تم الإنشاء",
      submitted: "تم التقديم",
      approved: "تم الموافقة",
      rejected: "تم الرفض",
      completed: "تم الإكمال",
    }
    return actionNames[action as keyof typeof actionNames] || action
  }

  const canApprove = (): boolean => {
    if (currentUser.role === "director" || currentUser.role === "deputy_director") {
      return request.status === "submitted"
    }
    if (currentUser.role === "accountant") {
      return request.status === "director_approved"
    }
    return false
  }

  const canReject = (): boolean => {
    return (
      ((currentUser.role === "director" || currentUser.role === "deputy_director") &&
        (request.status === "submitted" || request.status === "director_approved")) ||
      (currentUser.role === "accountant" && request.status === "director_approved")
    )
  }

  const canComplete = (): boolean => {
    return currentUser.role === "cashier" && request.status === "accountant_approved"
  }

  const handleAction = async () => {
    if (!actionType) return

    setIsLoading(true)
    setError(null)

    try {
      let newStatus = request.status
      let actionName = ""

      if (actionType === "approve") {
        if (currentUser.role === "director" || currentUser.role === "deputy_director") {
          newStatus = "director_approved"
          actionName = "approved"
        } else if (currentUser.role === "accountant") {
          newStatus = "accountant_approved"
          actionName = "approved"
        }
      } else if (actionType === "reject") {
        newStatus = "rejected"
        actionName = "rejected"
      } else if (actionType === "complete") {
        newStatus = "completed"
        actionName = "completed"
      }

      // Update request status
      const updateData: any = {
        status: newStatus,
        updated_at: new Date().toISOString(),
      }

      if (actionType === "approve") {
        if (currentUser.role === "director" || currentUser.role === "deputy_director") {
          updateData.director_approved_at = new Date().toISOString()
        } else if (currentUser.role === "accountant") {
          updateData.accountant_approved_at = new Date().toISOString()
        }
      } else if (actionType === "reject") {
        updateData.rejected_at = new Date().toISOString()
        updateData.rejection_reason = comments.trim() || null
      } else if (actionType === "complete") {
        updateData.completed_at = new Date().toISOString()
      }

      const { error: updateError } = await supabase.from("cash_requests").update(updateData).eq("id", request.id)

      if (updateError) throw updateError

      // Add workflow history entry
      await supabase.from("workflow_history").insert([
        {
          cash_request_id: request.id,
          user_id: currentUser.id,
          action: actionName,
          from_status: request.status,
          to_status: newStatus,
          comments: comments.trim() || null,
        },
      ])

      setIsActionDialogOpen(false)
      setActionType(null)
      setComments("")
      router.refresh()
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : "حدث خطأ أثناء تنفيذ العملية")
    } finally {
      setIsLoading(false)
    }
  }

  const openActionDialog = (action: "approve" | "reject" | "complete") => {
    setActionType(action)
    setComments("")
    setError(null)
    setIsActionDialogOpen(true)
  }

  return (
    <div className="space-y-6">
      {error && <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">{error}</div>}

      {/* Request Details */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-xl">طلب رقم: {request.request_number}</CardTitle>
              <CardDescription>تفاصيل طلب النقدية</CardDescription>
            </div>
            <Badge className={getStatusBadgeColor(request.status)} variant="secondary">
              {getStatusDisplayName(request.status)}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold text-gray-900">مقدم الطلب</h4>
              <p className="text-gray-600">{request.cashier.full_name}</p>
              <p className="text-sm text-gray-500">{request.cashier.email}</p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900">المبلغ المطلوب</h4>
              <p className="text-2xl font-bold text-blue-600">{request.amount.toLocaleString("ar-MA")} درهم</p>
            </div>
          </div>

          <div>
            <h4 className="font-semibold text-gray-900">الغرض من الطلب</h4>
            <p className="text-gray-600">{request.purpose}</p>
          </div>

          {request.description && (
            <div>
              <h4 className="font-semibold text-gray-900">الوصف التفصيلي</h4>
              <p className="text-gray-600">{request.description}</p>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold text-gray-900">تاريخ الإنشاء</h4>
              <p className="text-gray-600">{new Date(request.created_at).toLocaleString("ar-MA")}</p>
            </div>
            {request.submitted_at && (
              <div>
                <h4 className="font-semibold text-gray-900">تاريخ التقديم</h4>
                <p className="text-gray-600">{new Date(request.submitted_at).toLocaleString("ar-MA")}</p>
              </div>
            )}
          </div>

          {request.rejection_reason && (
            <div className="bg-red-50 p-4 rounded-md">
              <h4 className="font-semibold text-red-900">سبب الرفض</h4>
              <p className="text-red-700">{request.rejection_reason}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-4 space-x-reverse pt-4">
            {canApprove() && (
              <Button className="bg-green-600 hover:bg-green-700" onClick={() => openActionDialog("approve")}>
                موافقة
              </Button>
            )}
            {canReject() && (
              <Button variant="destructive" onClick={() => openActionDialog("reject")}>
                رفض
              </Button>
            )}
            {canComplete() && (
              <Button className="bg-blue-600 hover:bg-blue-700" onClick={() => openActionDialog("complete")}>
                إكمال الطلب
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Workflow History */}
      <Card>
        <CardHeader>
          <CardTitle>سجل الموافقات</CardTitle>
          <CardDescription>تتبع مراحل الموافقة على الطلب</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {workflowHistory.map((item, index) => (
              <div key={item.id} className="flex items-start space-x-4 space-x-reverse">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-semibold text-blue-600">{index + 1}</span>
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {getActionDisplayName(item.action)} - {getRoleDisplayName(item.user.role)}
                      </p>
                      <p className="text-sm text-gray-600">{item.user.full_name}</p>
                      {item.comments && (
                        <p className="text-sm text-gray-500 mt-1 bg-gray-50 p-2 rounded">{item.comments}</p>
                      )}
                    </div>
                    <p className="text-xs text-gray-400">{new Date(item.created_at).toLocaleString("ar-MA")}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Action Dialog */}
      <Dialog open={isActionDialogOpen} onOpenChange={setIsActionDialogOpen}>
        <DialogContent className="sm:max-w-md" dir="rtl">
          <DialogHeader>
            <DialogTitle>
              {actionType === "approve" && "موافقة على الطلب"}
              {actionType === "reject" && "رفض الطلب"}
              {actionType === "complete" && "إكمال الطلب"}
            </DialogTitle>
            <DialogDescription>طلب رقم: {request.request_number}</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="comments">
                {actionType === "approve" && "ملاحظات (اختياري)"}
                {actionType === "reject" && "سبب الرفض"}
                {actionType === "complete" && "ملاحظات الإكمال (اختياري)"}
              </Label>
              <Textarea
                id="comments"
                rows={3}
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                className="text-right"
                placeholder={
                  actionType === "approve"
                    ? "أدخل أي ملاحظات إضافية..."
                    : actionType === "reject"
                      ? "أدخل سبب رفض الطلب..."
                      : "أدخل ملاحظات الإكمال..."
                }
                required={actionType === "reject"}
              />
            </div>
            <div className="flex justify-end space-x-2 space-x-reverse">
              <Button type="button" variant="outline" onClick={() => setIsActionDialogOpen(false)}>
                إلغاء
              </Button>
              <Button
                type="button"
                onClick={handleAction}
                disabled={isLoading || (actionType === "reject" && !comments.trim())}
                className={
                  actionType === "approve"
                    ? "bg-green-600 hover:bg-green-700"
                    : actionType === "complete"
                      ? "bg-blue-600 hover:bg-blue-700"
                      : ""
                }
                variant={actionType === "reject" ? "destructive" : "default"}
              >
                {isLoading
                  ? "جاري التنفيذ..."
                  : actionType === "approve"
                    ? "موافقة"
                    : actionType === "reject"
                      ? "رفض"
                      : "إكمال"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
