"use client"

import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

interface DashboardProps {
  userProfile: any
  stats: any
  recentRequests: any[]
}

export function AccountantDashboard({ userProfile, stats, recentRequests }: DashboardProps) {
  const getStatusDisplayName = (status: string): string => {
    const statusNames = {
      draft: "مسودة",
      submitted: "مقدم للموافقة",
      director_approved: "موافقة المدير",
      accountant_approved: "موافقة المحاسب",
      completed: "مكتمل",
      rejected: "مرفوض",
    }
    return statusNames[status as keyof typeof statusNames] || status
  }

  const getStatusBadgeColor = (status: string): string => {
    const colors = {
      draft: "bg-gray-100 text-gray-800",
      submitted: "bg-yellow-100 text-yellow-800",
      director_approved: "bg-blue-100 text-blue-800",
      accountant_approved: "bg-green-100 text-green-800",
      completed: "bg-green-100 text-green-800",
      rejected: "bg-red-100 text-red-800",
    }
    return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800"
  }

  return (
    <div className="space-y-8">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الطلبات</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <p className="text-xs text-muted-foreground">جميع الطلبات في النظام</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">في انتظار مراجعتك</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.pendingApprovals}</div>
            <p className="text-xs text-muted-foreground">تحتاج للمراجعة المحاسبية</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">تم اعتمادها محاسبي</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.accountant_approved}</div>
            <p className="text-xs text-muted-foreground">جاهزة للتنفيذ</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المكتملة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.completed}</div>
            <p className="text-xs text-muted-foreground">تم تنفيذها بالكامل</p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>الإجراءات المحاسبية</CardTitle>
          <CardDescription>العمليات المحاسبية الأساسية</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a
              href="/requests?filter=pending_accountant"
              className="bg-orange-600 hover:bg-orange-700 text-white p-4 rounded-lg text-center block transition-colors"
            >
              <h3 className="font-semibold text-lg">المراجعة المحاسبية</h3>
              <p className="text-sm opacity-90">مراجعة الطلبات المعتمدة إدار</p>
            </a>

            <a
              href="/requests"
              className="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg text-center block transition-colors"
            >
              <h3 className="font-semibold text-lg">جميع الطلبات</h3>
              <p className="text-sm opacity-90">عرض جميع طلبات النقدية</p>
            </a>

            <a
              href="/reports"
              className="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg text-center block transition-colors"
            >
              <h3 className="font-semibold text-lg">التقارير المالية</h3>
              <p className="text-sm opacity-90">تقارير مالية مفصلة</p>
            </a>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>الطلبات الأخيرة</CardTitle>
          <CardDescription>آخر الطلبات في النظام</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentRequests.length > 0 ? (
              recentRequests.map((request) => (
                <div key={request.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <h4 className="font-medium">طلب رقم: {request.request_number}</h4>
                      <Badge className={getStatusBadgeColor(request.status)}>
                        {getStatusDisplayName(request.status)}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      مقدم من: {request.cashier.full_name} - {request.purpose}
                    </p>
                    <p className="text-sm text-gray-500">
                      المبلغ: {Number(request.amount).toLocaleString("en-US")} درهم
                    </p>
                  </div>
                  <Button size="sm" variant="outline" asChild>
                    <a href={`/requests/${request.id}`}>مراجعة</a>
                  </Button>
                </div>
              ))
            ) : (
              <p className="text-center text-gray-500 py-8">لا توجد طلبات حتى الآن</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
