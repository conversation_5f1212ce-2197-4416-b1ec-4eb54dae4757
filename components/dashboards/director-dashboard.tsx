"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

interface DashboardProps {
  userProfile: any
  stats: any
  recentRequests: any[]
}

export function DirectorDashboard({ userProfile, stats, recentRequests }: DashboardProps) {
  const getStatusDisplayName = (status: string): string => {
    const statusNames = {
      draft: "مسودة",
      submitted: "مقدم للموافقة",
      director_approved: "موافقة المدير",
      accountant_approved: "موافقة المحاسب",
      completed: "مكتمل",
      rejected: "مرفوض",
    }
    return statusNames[status as keyof typeof statusNames] || status
  }

  const getStatusBadgeColor = (status: string): string => {
    const colors = {
      draft: "bg-gray-100 text-gray-800",
      submitted: "bg-yellow-100 text-yellow-800",
      director_approved: "bg-blue-100 text-blue-800",
      accountant_approved: "bg-green-100 text-green-800",
      completed: "bg-green-100 text-green-800",
      rejected: "bg-red-100 text-red-800",
    }
    return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800"
  }

  return (
    <div className="space-y-8">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الطلبات</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <p className="text-xs text-muted-foreground">جميع الطلبات في النظام</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">في انتظار موافقتك</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.pendingApprovals}</div>
            <p className="text-xs text-muted-foreground">تحتاج لمراجعة فورية</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">تم الموافقة عليها</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {stats.director_approved + stats.accountant_approved + stats.completed}
            </div>
            <p className="text-xs text-muted-foreground">طلبات معتمدة</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المرفوضة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
            <p className="text-xs text-muted-foreground">طلبات مرفوضة</p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>الإجراءات الإدارية</CardTitle>
          <CardDescription>العمليات الإدارية الأساسية</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a
              href="/requests?filter=pending"
              className="bg-red-600 hover:bg-red-700 text-white p-4 rounded-lg text-center block transition-colors"
            >
              <h3 className="font-semibold text-lg">الطلبات المعلقة</h3>
              <p className="text-sm opacity-90">مراجعة الطلبات في انتظار الموافقة</p>
            </a>

            <a
              href="/requests"
              className="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg text-center block transition-colors"
            >
              <h3 className="font-semibold text-lg">جميع الطلبات</h3>
              <p className="text-sm opacity-90">عرض جميع طلبات النقدية</p>
            </a>

            <a
              href="/users"
              className="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg text-center block transition-colors"
            >
              <h3 className="font-semibold text-lg">إدارة المستخدمين</h3>
              <p className="text-sm opacity-90">إضافة وتعديل المستخدمين</p>
            </a>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>النشاط الأخير</CardTitle>
          <CardDescription>آخر الطلبات المقدمة في النظام</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentRequests.length > 0 ? (
              recentRequests.map((request) => (
                <div key={request.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <h4 className="font-medium">طلب رقم: {request.request_number}</h4>
                      <Badge className={getStatusBadgeColor(request.status)}>
                        {getStatusDisplayName(request.status)}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      مقدم من: {request.cashier.full_name} - {request.purpose}
                    </p>
                    <p className="text-sm text-gray-500">
                      المبلغ: {Number(request.amount).toLocaleString("ar-MA")} درهم
                    </p>
                  </div>
                  <Button size="sm" variant="outline" asChild>
                    <a href={`/requests/${request.id}`}>مراجعة</a>
                  </Button>
                </div>
              ))
            ) : (
              <p className="text-center text-gray-500 py-8">لا توجد طلبات حتى الآن</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Financial Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>إجمالي المبالغ المطلوبة</CardTitle>
            <CardDescription>قيمة جميع الطلبات</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {stats.totalAmount.toLocaleString("ar-MA")} درهم
            </div>
            <p className="text-sm text-gray-600">إجمالي قيمة جميع الطلبات</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>معدل الموافقة</CardTitle>
            <CardDescription>نسبة الطلبات المعتمدة</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-600 mb-2">
              {stats.total > 0
                ? Math.round(
                    ((stats.director_approved + stats.accountant_approved + stats.completed) / stats.total) * 100,
                  )
                : 0}
              %
            </div>
            <p className="text-sm text-gray-600">من إجمالي الطلبات المقدمة</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
