"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

interface DashboardProps {
  userProfile: any
  stats: any
  recentRequests: any[]
}

export function CashierDashboard({ userProfile, stats, recentRequests }: DashboardProps) {
  const getStatusDisplayName = (status: string): string => {
    const statusNames = {
      draft: "مسودة",
      submitted: "مقدم للموافقة",
      director_approved: "موافقة المدير",
      accountant_approved: "موافقة المحاسب",
      completed: "مكتمل",
      rejected: "مرفوض",
    }
    return statusNames[status as keyof typeof statusNames] || status
  }

  const getStatusBadgeColor = (status: string): string => {
    const colors = {
      draft: "bg-gray-100 text-gray-800",
      submitted: "bg-yellow-100 text-yellow-800",
      director_approved: "bg-blue-100 text-blue-800",
      accountant_approved: "bg-green-100 text-green-800",
      completed: "bg-green-100 text-green-800",
      rejected: "bg-red-100 text-red-800",
    }
    return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800"
  }

  return (
    <div className="space-y-8">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الطلبات</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <p className="text-xs text-muted-foreground">جميع طلباتك</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المسودات</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{stats.draft}</div>
            <p className="text-xs text-muted-foreground">طلبات غير مقدمة</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">جاهزة للتنفيذ</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.accountant_approved}</div>
            <p className="text-xs text-muted-foreground">تحتاج لعمليات مالية</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المكتملة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.completed}</div>
            <p className="text-xs text-muted-foreground">تم الانتهاء منها</p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>الإجراءات السريعة</CardTitle>
          <CardDescription>العمليات الأكثر استخداماً</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a
              href="/requests/new"
              className="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg text-center block transition-colors"
            >
              <h3 className="font-semibold text-lg">طلب نقدية جديد</h3>
              <p className="text-sm opacity-90">إنشاء طلب نقدية جديد</p>
            </a>

            <a
              href="/requests"
              className="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg text-center block transition-colors"
            >
              <h3 className="font-semibold text-lg">عرض الطلبات</h3>
              <p className="text-sm opacity-90">عرض جميع طلباتك</p>
            </a>

            <a
              href="/operations"
              className="bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg text-center block transition-colors"
            >
              <h3 className="font-semibold text-lg">العمليات المالية</h3>
              <p className="text-sm opacity-90">إدارة الاستلام والتسليم</p>
            </a>
          </div>
        </CardContent>
      </Card>

      {/* Recent Requests */}
      <Card>
        <CardHeader>
          <CardTitle>الطلبات الأخيرة</CardTitle>
          <CardDescription>آخر 5 طلبات قمت بإنشائها</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentRequests.length > 0 ? (
              recentRequests.map((request) => (
                <div key={request.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <h4 className="font-medium">طلب رقم: {request.request_number}</h4>
                      <Badge className={getStatusBadgeColor(request.status)}>
                        {getStatusDisplayName(request.status)}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{request.purpose}</p>
                    <p className="text-sm text-gray-500">
                      المبلغ: {Number(request.amount).toLocaleString("ar-MA")} درهم
                    </p>
                  </div>
                  <Button size="sm" variant="outline" asChild>
                    <a href={`/requests/${request.id}`}>عرض</a>
                  </Button>
                </div>
              ))
            ) : (
              <p className="text-center text-gray-500 py-8">لا توجد طلبات حتى الآن</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Financial Summary */}
      <Card>
        <CardHeader>
          <CardTitle>الملخص المالي</CardTitle>
          <CardDescription>إجمالي المبالغ المطلوبة</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-blue-600 mb-2">{stats.totalAmount.toLocaleString("ar-MA")} درهم</div>
          <p className="text-sm text-gray-600">إجمالي قيمة جميع طلباتك</p>
        </CardContent>
      </Card>
    </div>
  )
}
