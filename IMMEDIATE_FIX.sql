-- IMMEDIATE FIX for race condition in request number generation
-- Run this directly in your Supabase SQL editor or database console

-- Step 1: Drop existing trigger and functions
DROP TRIGGER IF EXISTS set_cash_request_number ON public.cash_requests;
DROP FUNCTION IF EXISTS set_request_number();
DROP FUNCTION IF EXISTS generate_request_number();

-- Step 2: Create the new race-condition-free function
CREATE OR REPLACE FUNCTION generate_request_number()
RETURNS TEXT AS $$
DECLARE
  year_part TEXT;
  sequence_num INTEGER;
  request_num TEXT;
  lock_key BIGINT;
BEGIN
  year_part := EXTRACT(YEAR FROM NOW())::TEXT;
  
  -- Create a unique lock key based on the year
  lock_key := ('x' || substr(md5(year_part), 1, 15))::bit(60)::bigint;
  
  -- Acquire advisory lock to ensure only one process generates numbers at a time
  PERFORM pg_advisory_lock(lock_key);
  
  BEGIN
    -- Get the next sequence number for this year atomically
    SELECT COALESCE(MAX(
      CASE 
        WHEN request_number ~ ('^' || year_part || '-[0-9]+$') 
        THEN CAST(SPLIT_PART(request_number, '-', 2) AS INTEGER)
        ELSE 0 
      END
    ), 0) + 1
    INTO sequence_num
    FROM public.cash_requests;
    
    -- Format: YYYY-NNNN (e.g., 2024-0001)
    request_num := year_part || '-' || LPAD(sequence_num::TEXT, 4, '0');
    
    -- Release the advisory lock
    PERFORM pg_advisory_unlock(lock_key);
    
    RETURN request_num;
    
  EXCEPTION
    WHEN OTHERS THEN
      -- Make sure to release the lock even if an error occurs
      PERFORM pg_advisory_unlock(lock_key);
      RAISE;
  END;
END;
$$ LANGUAGE plpgsql;

-- Step 3: Create the trigger function
CREATE OR REPLACE FUNCTION set_request_number()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.request_number IS NULL OR NEW.request_number = '' THEN
    NEW.request_number := generate_request_number();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Recreate the trigger
CREATE TRIGGER set_cash_request_number 
  BEFORE INSERT ON public.cash_requests 
  FOR EACH ROW EXECUTE FUNCTION set_request_number();

-- Verification: Test the function
SELECT generate_request_number() as test_request_number;
