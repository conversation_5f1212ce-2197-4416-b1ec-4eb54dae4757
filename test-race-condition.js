// Test script to simulate race condition for request number generation
// This script creates multiple concurrent requests to test the fix

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://ncfnlrxwovjgysuxpdij.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5jZm5scnh3b3ZqZ3lzdXhwZGlqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTgzMDIwMzMsImV4cCI6MjA3Mzg3ODAzM30.MmRroToiFKfBdb-JISuu2u1WZSdYiLCDtp63hQ-aRhU'

const supabase = createClient(supabaseUrl, supabaseKey)

async function createTestRequest(userId, index) {
  try {
    const requestData = {
      cashier_id: userId,
      amount: 100.00 + index,
      purpose: `Test request ${index}`,
      description: `Test description for request ${index}`,
      status: 'draft',
      submitted_at: null,
    }

    console.log(`Creating request ${index}...`)
    
    const { data, error } = await supabase
      .from("cash_requests")
      .insert([requestData])
      .select()
      .single()

    if (error) {
      console.error(`Request ${index} failed:`, error.message)
      return { success: false, error: error.message, index }
    }

    console.log(`Request ${index} created successfully with number: ${data.request_number}`)
    return { success: true, data, index }
  } catch (error) {
    console.error(`Request ${index} exception:`, error.message)
    return { success: false, error: error.message, index }
  }
}

async function testRaceCondition() {
  console.log('Testing race condition with concurrent requests...')
  
  // You'll need to replace this with an actual user ID from your database
  const testUserId = '11111111-1111-1111-1111-111111111111' // Replace with actual cashier user ID
  
  // Create 10 concurrent requests
  const promises = []
  for (let i = 1; i <= 10; i++) {
    promises.push(createTestRequest(testUserId, i))
  }

  try {
    const results = await Promise.all(promises)
    
    const successful = results.filter(r => r.success)
    const failed = results.filter(r => !r.success)
    
    console.log('\n=== RESULTS ===')
    console.log(`Successful requests: ${successful.length}`)
    console.log(`Failed requests: ${failed.length}`)
    
    if (successful.length > 0) {
      console.log('\nSuccessful request numbers:')
      successful.forEach(r => {
        console.log(`  Request ${r.index}: ${r.data.request_number}`)
      })
    }
    
    if (failed.length > 0) {
      console.log('\nFailed requests:')
      failed.forEach(r => {
        console.log(`  Request ${r.index}: ${r.error}`)
      })
    }
    
    // Check for duplicate request numbers
    const requestNumbers = successful.map(r => r.data.request_number)
    const uniqueNumbers = [...new Set(requestNumbers)]
    
    if (requestNumbers.length !== uniqueNumbers.length) {
      console.log('\n❌ DUPLICATE REQUEST NUMBERS DETECTED!')
      console.log('Request numbers:', requestNumbers)
    } else {
      console.log('\n✅ All request numbers are unique!')
    }
    
  } catch (error) {
    console.error('Test failed:', error)
  }
}

// Run the test
testRaceCondition().then(() => {
  console.log('\nTest completed.')
  process.exit(0)
}).catch(error => {
  console.error('Test error:', error)
  process.exit(1)
})
