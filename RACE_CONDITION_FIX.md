# Race Condition Fix for Request Number Generation

## Problem Description

The application was experiencing a PostgreSQL unique constraint violation error:

```
{"code":"23505","details":null,"hint":null,"message":"duplicate key value violates unique constraint \"cash_requests_request_number_key\""}
```

This error occurred when multiple users tried to create cash requests simultaneously, causing a race condition in the request number generation process.

## Root Cause Analysis

The original `generate_request_number()` function had a race condition:

1. **Step 1**: Function reads the current maximum sequence number from the database
2. **Step 2**: Function adds 1 to get the next sequence number  
3. **Step 3**: Function returns the new request number (e.g., "2024-0005")

**The Problem**: If two requests are created at exactly the same time:
- Both functions read the same maximum number (e.g., 4)
- Both calculate the next number as 5
- Both try to insert with request number "2024-0005"
- The second insert fails with a unique constraint violation

## Solutions Implemented

### 1. Client-Side Retry Logic (Immediate Fix)

Modified `components/cash-request-form.tsx` to handle the duplicate key error gracefully:

```typescript
// Retry logic for handling duplicate request number race condition
let retryCount = 0
const maxRetries = 3

while (retryCount < maxRetries) {
  try {
    const result = await supabase.from("cash_requests").insert([requestData]).select().single()
    // ... handle success
    break
  } catch (insertError: any) {
    // Check if it's a duplicate key error for request_number
    if (insertError.code === "23505" && insertError.message.includes("cash_requests_request_number_key")) {
      retryCount++
      if (retryCount >= maxRetries) {
        throw new Error("فشل في إنشاء رقم طلب فريد. يرجى المحاولة مرة أخرى.")
      }
      // Wait a short random time before retrying
      await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50))
      continue
    } else {
      throw insertError
    }
  }
}
```

**Benefits**:
- ✅ Immediate fix that works with current database schema
- ✅ Graceful error handling with user-friendly messages
- ✅ Random delay reduces collision probability
- ✅ Limited retry attempts prevent infinite loops

### 2. Database-Level Fix (Long-term Solution)

Created improved database functions in `scripts/010_fix_request_number_race_condition.sql`:

#### A. PostgreSQL Sequence-Based Generation
```sql
-- Create sequence for request numbers (reset yearly)
CREATE SEQUENCE IF NOT EXISTS request_number_seq;

-- Improved function using sequence (thread-safe)
CREATE OR REPLACE FUNCTION generate_request_number()
RETURNS TEXT AS $$
DECLARE
  year_part TEXT;
  sequence_num INTEGER;
  request_num TEXT;
BEGIN
  year_part := EXTRACT(YEAR FROM NOW())::TEXT;
  
  -- Get next sequence number (thread-safe)
  sequence_num := nextval('request_number_seq');
  
  -- Format: YYYY-NNNN (e.g., 2024-0001)
  request_num := year_part || '-' || LPAD(sequence_num::TEXT, 4, '0');
  
  -- Double-check for uniqueness (safety net)
  WHILE EXISTS (SELECT 1 FROM public.cash_requests WHERE request_number = request_num) LOOP
    sequence_num := nextval('request_number_seq');
    request_num := year_part || '-' || LPAD(sequence_num::TEXT, 4, '0');
  END LOOP;
  
  RETURN request_num;
END;
$$ LANGUAGE plpgsql;
```

#### B. Safe Request Creation Function
Created `scripts/011_create_safe_request_function.sql` with a database function that handles retries:

```sql
CREATE OR REPLACE FUNCTION create_cash_request(
  p_cashier_id UUID,
  p_amount DECIMAL(15,2),
  p_purpose TEXT,
  p_description TEXT DEFAULT NULL,
  p_status TEXT DEFAULT 'draft',
  p_submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS TABLE(...) AS $$
-- Function includes built-in retry logic and permission checks
```

**Benefits**:
- ✅ Thread-safe sequence generation
- ✅ Built-in retry logic at database level
- ✅ Permission validation
- ✅ Atomic operations
- ✅ Better performance (fewer round trips)

### 3. Hybrid Approach (Current Implementation)

The current implementation tries the safe database function first, then falls back to client-side retry:

```typescript
try {
  // Try using the safe database function
  const { data: functionResult, error: functionError } = await supabase.rpc('create_cash_request', {
    p_cashier_id: userId,
    p_amount: amount,
    p_purpose: formData.purpose.trim(),
    // ... other parameters
  })
  
  if (functionError) throw functionError
  data = functionResult[0]
} catch (functionError: any) {
  // Fallback to direct insert with retry logic
  // ... retry implementation
}
```

## Testing

Created `test-race-condition.js` to simulate concurrent request creation:

```bash
node test-race-condition.js
```

This script creates 10 concurrent requests to verify that:
- No duplicate request numbers are generated
- All requests are created successfully
- The retry mechanism works correctly

## Migration Steps

To apply the database-level fixes:

1. **Apply the sequence-based fix**:
   ```sql
   -- Run scripts/010_fix_request_number_race_condition.sql
   ```

2. **Apply the safe function**:
   ```sql
   -- Run scripts/011_create_safe_request_function.sql
   ```

3. **Update client code** (already done):
   - Modified `components/cash-request-form.tsx` with retry logic

## Monitoring

To monitor for any remaining issues:

1. **Check for constraint violations**:
   ```sql
   SELECT * FROM pg_stat_database_conflicts WHERE datname = 'postgres';
   ```

2. **Monitor request number uniqueness**:
   ```sql
   SELECT request_number, COUNT(*) 
   FROM cash_requests 
   GROUP BY request_number 
   HAVING COUNT(*) > 1;
   ```

3. **Check application logs** for retry attempts and errors

## Prevention

To prevent similar issues in the future:

1. ✅ Use database sequences for auto-incrementing values
2. ✅ Implement retry logic for race-prone operations
3. ✅ Add unique constraints to enforce data integrity
4. ✅ Test concurrent operations during development
5. ✅ Monitor database constraint violations in production

## Status

- ✅ **Client-side fix**: Implemented and tested
- ⏳ **Database-level fix**: Scripts created, needs deployment
- ✅ **Testing**: Race condition test script created
- ✅ **Documentation**: Complete

The immediate issue is resolved with the client-side retry logic. The database-level improvements can be applied when database access is available.
