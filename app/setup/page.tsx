"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

export default function SetupPage() {
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const checkAuth = async () => {
      const { data, error } = await supabase.auth.getUser()
      if (error || !data?.user) {
        router.push("/auth/login")
        return
      }
      setLoading(false)
    }

    checkAuth()
  }, [router, supabase])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جارٍ التحقق من الهوية...</p>
        </div>
      </div>
    )
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4"
      dir="rtl"
    >
      <div className="w-full max-w-2xl">
        <Card className="shadow-xl">
          <CardHeader className="text-center">
            <CardTitle className="text-3xl font-bold text-gray-900">إعداد قاعدة البيانات</CardTitle>
            <CardDescription className="text-gray-600 text-lg">
              مرحباً بك في نظام طلبات النقدية. يجب إعداد قاعدة البيانات أولاً.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h3 className="font-semibold text-yellow-800 mb-2">خطوات الإعداد المطلوبة:</h3>
              <ol className="list-decimal list-inside space-y-2 text-yellow-700">
                <li>
                  تشغيل ملف <code className="bg-yellow-100 px-2 py-1 rounded">001_create_users_table.sql</code>
                </li>
                <li>
                  تشغيل ملف <code className="bg-yellow-100 px-2 py-1 rounded">002_create_cash_requests_table.sql</code>
                </li>
                <li>
                  تشغيل ملف{" "}
                  <code className="bg-yellow-100 px-2 py-1 rounded">003_create_workflow_history_table.sql</code>
                </li>
                <li>
                  تشغيل ملف <code className="bg-yellow-100 px-2 py-1 rounded">004_create_operations_table.sql</code>
                </li>
                <li>
                  تشغيل ملف{" "}
                  <code className="bg-yellow-100 px-2 py-1 rounded">005_create_functions_and_triggers.sql</code>
                </li>
                <li>
                  تشغيل ملف <code className="bg-yellow-100 px-2 py-1 rounded">006_create_user_profile_trigger.sql</code>
                </li>
              </ol>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-blue-800 mb-2">معلومات مهمة:</h3>
              <ul className="list-disc list-inside space-y-1 text-blue-700">
                <li>يجب تشغيل الملفات بالترتيب المذكور أعلاه</li>
                <li>ستقوم الملفات بإنشاء جميع الجداول والوظائف المطلوبة</li>
                <li>سيتم إنشاء ملف تعريف المستخدم تلقائياً عند أول تسجيل دخول</li>
                <li>جميع الجداول محمية بسياسات الأمان (RLS)</li>
              </ul>
            </div>

            <div className="text-center">
              <p className="text-gray-600 mb-4">بعد تشغيل جميع الملفات، يمكنك الانتقال إلى لوحة التحكم</p>
              <Button onClick={() => (window.location.href = "/dashboard")} className="bg-blue-600 hover:bg-blue-700">
                الانتقال إلى لوحة التحكم
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
