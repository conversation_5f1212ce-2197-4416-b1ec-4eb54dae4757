import { redirect } from "next/navigation"
import { createClient } from "@/lib/supabase/server"
import { CashierDashboard } from "@/components/dashboards/cashier-dashboard"
import { DirectorDashboard } from "@/components/dashboards/director-dashboard"
import { AccountantDashboard } from "@/components/dashboards/accountant-dashboard"
import { Navigation } from "@/components/navigation"

export default async function DashboardPage() {
  const supabase = await createClient()

  const { data, error } = await supabase.auth.getUser()
  if (error || !data?.user) {
    redirect("/auth/login")
  }

  try {
    // Get user profile with role
    const { data: userProfile, error: profileError } = await supabase
      .from("users")
      .select("*")
      .eq("id", data.user.id)
      .single()

    if (profileError) {
      console.error("Database error:", profileError)
      // If tables don't exist, show setup message
      if (profileError.code === "42P01") {
        return (
          <div className="min-h-screen bg-gray-50 flex items-center justify-center" dir="rtl">
            <div className="max-w-md mx-auto text-center p-6 bg-white rounded-lg shadow-lg">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">إعداد قاعدة البيانات مطلوب</h1>
              <p className="text-gray-600 mb-4">يجب تشغيل سكريبت قاعدة البيانات أولاً لإنشاء الجداول المطلوبة.</p>
              <p className="text-sm text-gray-500">يرجى تشغيل جميع ملفات SQL في مجلد scripts بالترتيب الصحيح.</p>
            </div>
          </div>
        )
      }
      redirect("/auth/login")
    }

    if (!userProfile) {
      redirect("/auth/login")
    }

    // Get comprehensive statistics based on role
    let requestsQuery = supabase.from("cash_requests").select("*")

    // Filter requests based on user role
    if (userProfile.role === "cashier") {
      requestsQuery = requestsQuery.eq("cashier_id", data.user.id)
    }

    const { data: allRequests } = await requestsQuery

    // Get recent requests for activity feed
    let recentRequestsQuery = supabase
      .from("cash_requests")
      .select(`
        *,
        cashier:users!cash_requests_cashier_id_fkey(full_name, email)
      `)
      .order("created_at", { ascending: false })
      .limit(5)

    if (userProfile.role === "cashier") {
      recentRequestsQuery = recentRequestsQuery.eq("cashier_id", data.user.id)
    }

    const { data: recentRequests } = await recentRequestsQuery

    // Get pending approvals count for directors and accountants
    let pendingApprovalsCount = 0
    if (userProfile.role === "director" || userProfile.role === "deputy_director") {
      const { count } = await supabase
        .from("cash_requests")
        .select("*", { count: "exact", head: true })
        .eq("status", "submitted")
      pendingApprovalsCount = count || 0
    } else if (userProfile.role === "accountant") {
      const { count } = await supabase
        .from("cash_requests")
        .select("*", { count: "exact", head: true })
        .eq("status", "director_approved")
      pendingApprovalsCount = count || 0
    }

    // Calculate statistics
    const stats = {
      total: allRequests?.length || 0,
      draft: allRequests?.filter((r) => r.status === "draft").length || 0,
      submitted: allRequests?.filter((r) => r.status === "submitted").length || 0,
      director_approved: allRequests?.filter((r) => r.status === "director_approved").length || 0,
      accountant_approved: allRequests?.filter((r) => r.status === "accountant_approved").length || 0,
      completed: allRequests?.filter((r) => r.status === "completed").length || 0,
      rejected: allRequests?.filter((r) => r.status === "rejected").length || 0,
      totalAmount: allRequests?.reduce((sum, r) => sum + Number(r.amount), 0) || 0,
      pendingApprovals: pendingApprovalsCount,
    }

    const dashboardProps = {
      userProfile,
      stats,
      recentRequests: recentRequests || [],
    }

    return (
      <div className="min-h-screen bg-gray-50" dir="rtl">
        <Navigation user={data.user} userProfile={userProfile} />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {userProfile.role === "cashier" && <CashierDashboard {...dashboardProps} />}
          {(userProfile.role === "director" || userProfile.role === "deputy_director") && (
            <DirectorDashboard {...dashboardProps} />
          )}
          {userProfile.role === "accountant" && <AccountantDashboard {...dashboardProps} />}
        </div>
      </div>
    )
  } catch (error) {
    console.error("Dashboard error:", error)
    redirect("/auth/login")
  }
}

function getRoleDisplayName(role: string): string {
  const roleNames = {
    cashier: "أمين الصندوق",
    director: "المدير العام",
    deputy_director: "نائب المدير",
    accountant: "المحاسب المالي",
  }
  return roleNames[role as keyof typeof roleNames] || role
}
