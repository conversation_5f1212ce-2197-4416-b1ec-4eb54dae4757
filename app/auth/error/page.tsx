import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default async function ErrorPage({
  searchParams,
}: {
  searchParams: Promise<{ error: string }>
}) {
  const params = await searchParams

  return (
    <div
      className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-pink-100 p-4"
      dir="rtl"
    >
      <div className="w-full max-w-md">
        <Card className="shadow-xl">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl text-red-600">عذراً، حدث خطأ</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            {params?.error ? (
              <p className="text-sm text-gray-600">رمز الخطأ: {params.error}</p>
            ) : (
              <p className="text-sm text-gray-600">حدث خطأ غير محدد</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
