import { redirect } from "next/navigation"
import { createClient } from "@/lib/supabase/server"
import { OperationsClient } from "@/components/operations-client"
import { Navigation } from "@/components/navigation"

export default async function OperationsPage() {
  const supabase = await createClient()

  const { data, error } = await supabase.auth.getUser()
  if (error || !data?.user) {
    redirect("/auth/login")
  }

  // Get user profile with role
  const { data: userProfile } = await supabase.from("users").select("*").eq("id", data.user.id).single()

  if (!userProfile || userProfile.role !== "cashier") {
    redirect("/dashboard")
  }

  // Get approved requests that need operations
  const { data: approvedRequests } = await supabase
    .from("cash_requests")
    .select(`
      *,
      operations(*)
    `)
    .eq("cashier_id", data.user.id)
    .eq("status", "accountant_approved")
    .order("accountant_approved_at", { ascending: false })

  // Get all operations for this cashier
  const { data: allOperations } = await supabase
    .from("operations")
    .select(`
      *,
      cash_request:cash_requests(request_number, purpose, amount)
    `)
    .eq("performed_by", data.user.id)
    .order("operation_date", { ascending: false })

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      <Navigation user={data.user} userProfile={userProfile} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">إدارة العمليات</h2>
          <p className="text-sm text-gray-600">إدارة عمليات استلام وتسليم النقدية</p>
        </div>

        <OperationsClient
          approvedRequests={approvedRequests || []}
          allOperations={allOperations || []}
          currentUser={userProfile}
        />
      </div>
    </div>
  )
}
