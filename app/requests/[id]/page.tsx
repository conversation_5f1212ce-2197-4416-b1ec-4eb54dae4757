import { redirect } from "next/navigation"
import { createClient } from "@/lib/supabase/server"
import { RequestDetailsClient } from "@/components/request-details-client"
import { Navigation } from "@/components/navigation"

export default async function RequestDetailsPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  const supabase = await createClient()

  const { data, error } = await supabase.auth.getUser()
  if (error || !data?.user) {
    redirect("/auth/login")
  }

  // Get user profile with role
  const { data: userProfile } = await supabase.from("users").select("*").eq("id", data.user.id).single()

  if (!userProfile) {
    redirect("/auth/login")
  }

  // Get request details with cashier info
  const { data: request } = await supabase
    .from("cash_requests")
    .select(`
      *,
      cashier:users!cash_requests_cashier_id_fkey(full_name, email, role)
    `)
    .eq("id", id)
    .single()

  if (!request) {
    redirect("/requests")
  }

  // Check if user has permission to view this request
  const canView =
    request.cashier_id === data.user.id || ["director", "deputy_director", "accountant"].includes(userProfile.role)

  if (!canView) {
    redirect("/requests")
  }

  // Get workflow history
  const { data: workflowHistory } = await supabase
    .from("workflow_history")
    .select(`
      *,
      user:users!workflow_history_user_id_fkey(full_name, role)
    `)
    .eq("cash_request_id", id)
    .order("created_at", { ascending: true })

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      <Navigation user={data.user} userProfile={userProfile} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">تفاصيل الطلب</h2>
          <p className="text-sm text-gray-600">طلب رقم: {request.request_number}</p>
          <div className="mt-4">
            <a href="/requests" className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm">
              العودة للطلبات
            </a>
          </div>
        </div>

        <RequestDetailsClient request={request} workflowHistory={workflowHistory || []} currentUser={userProfile} />
      </div>
    </div>
  )
}
