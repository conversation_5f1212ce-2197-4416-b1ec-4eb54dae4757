import { redirect } from "next/navigation"
import { createClient } from "@/lib/supabase/server"
import { RequestsListClient } from "@/components/requests-list-client"
import { Navigation } from "@/components/navigation"

export default async function RequestsPage() {
  const supabase = await createClient()

  const { data, error } = await supabase.auth.getUser()
  if (error || !data?.user) {
    redirect("/auth/login")
  }

  // Get user profile with role
  const { data: userProfile } = await supabase.from("users").select("*").eq("id", data.user.id).single()

  if (!userProfile) {
    redirect("/auth/login")
  }

  // Get requests based on user role
  let requestsQuery = supabase
    .from("cash_requests")
    .select(`
      *,
      cashier:users!cash_requests_cashier_id_fkey(full_name, email)
    `)
    .order("created_at", { ascending: false })

  // Filter requests based on user role
  if (userProfile.role === "cashier") {
    requestsQuery = requestsQuery.eq("cashier_id", data.user.id)
  }

  const { data: requests } = await requestsQuery

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      <Navigation user={data.user} userProfile={userProfile} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">طلبات النقدية</h2>
          <p className="text-sm text-gray-600">عرض وإدارة طلبات النقدية</p>
          {userProfile.role === "cashier" && (
            <div className="mt-4">
              <a href="/requests/new" className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm">
                طلب جديد
              </a>
            </div>
          )}
        </div>

        <RequestsListClient requests={requests || []} currentUser={userProfile} />
      </div>
    </div>
  )
}
