import { redirect } from "next/navigation"
import { createClient } from "@/lib/supabase/server"
import { CashRequestForm } from "@/components/cash-request-form"
import { Navigation } from "@/components/navigation"

export default async function NewRequestPage() {
  const supabase = await createClient()

  const { data, error } = await supabase.auth.getUser()
  if (error || !data?.user) {
    redirect("/auth/login")
  }

  // Get user profile with role
  const { data: userProfile } = await supabase.from("users").select("*").eq("id", data.user.id).single()

  if (!userProfile || userProfile.role !== "cashier") {
    redirect("/dashboard")
  }

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      <Navigation user={data.user} userProfile={userProfile} />

      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">طلب نقدية جديد</h2>
          <p className="text-sm text-gray-600">إنشاء طلب نقدية جديد</p>
        </div>

        <CashRequestForm userId={data.user.id} />
      </div>
    </div>
  )
}
