import { redirect } from "next/navigation"
import { createClient } from "@/lib/supabase/server"
import { UserManagementClient } from "@/components/user-management-client"
import { Navigation } from "@/components/navigation"

export default async function UsersPage() {
  const supabase = await createClient()

  const { data, error } = await supabase.auth.getUser()
  if (error || !data?.user) {
    redirect("/auth/login")
  }

  // Get user profile with role
  const { data: userProfile } = await supabase.from("users").select("*").eq("id", data.user.id).single()

  if (!userProfile || !["director", "deputy_director"].includes(userProfile.role)) {
    redirect("/dashboard")
  }

  // Get all users
  const { data: users } = await supabase.from("users").select("*").order("created_at", { ascending: false })

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      <Navigation user={data.user} userProfile={userProfile} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">إدارة المستخدمين</h2>
          <p className="text-sm text-gray-600">إضافة وتعديل وحذف المستخدمين</p>
        </div>

        <UserManagementClient users={users || []} currentUser={userProfile} />
      </div>
    </div>
  )
}
