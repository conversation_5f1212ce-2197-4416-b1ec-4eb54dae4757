-- Create a safe function to insert cash requests with automatic retry for request number conflicts
CREATE OR REPLACE FUNCTION create_cash_request(
  p_cashier_id UUID,
  p_amount DECIMAL(15,2),
  p_purpose TEXT,
  p_description TEXT DEFAULT NULL,
  p_status TEXT DEFAULT 'draft',
  p_submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS TABLE(
  id UUID,
  request_number TEXT,
  amount DECIMAL(15,2),
  purpose TEXT,
  description TEXT,
  status TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  submitted_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
  new_request_id UUID;
  new_request_number TEXT;
  retry_count INTEGER := 0;
  max_retries INTEGER := 5;
BEGIN
  -- Validate inputs
  IF p_amount <= 0 THEN
    RAISE EXCEPTION 'Amount must be greater than zero';
  END IF;
  
  IF p_purpose IS NULL OR trim(p_purpose) = '' THEN
    RAISE EXCEPTION 'Purpose is required';
  END IF;
  
  IF p_status NOT IN ('draft', 'submitted') THEN
    RAISE EXCEPTION 'Invalid status. Must be draft or submitted';
  END IF;

  -- Retry loop to handle race conditions
  WHILE retry_count < max_retries LOOP
    BEGIN
      -- Generate a new UUID for the request
      new_request_id := gen_random_uuid();
      
      -- Generate request number
      new_request_number := generate_request_number();
      
      -- Try to insert the request
      INSERT INTO public.cash_requests (
        id,
        cashier_id,
        request_number,
        amount,
        purpose,
        description,
        status,
        submitted_at
      ) VALUES (
        new_request_id,
        p_cashier_id,
        new_request_number,
        p_amount,
        trim(p_purpose),
        CASE WHEN p_description IS NOT NULL AND trim(p_description) != '' THEN trim(p_description) ELSE NULL END,
        p_status,
        p_submitted_at
      );
      
      -- If we get here, the insert was successful
      EXIT;
      
    EXCEPTION
      WHEN unique_violation THEN
        -- Check if it's the request_number constraint
        IF SQLERRM LIKE '%cash_requests_request_number_key%' THEN
          retry_count := retry_count + 1;
          IF retry_count >= max_retries THEN
            RAISE EXCEPTION 'Failed to generate unique request number after % attempts', max_retries;
          END IF;
          -- Small delay to reduce collision probability
          PERFORM pg_sleep(random() * 0.1);
        ELSE
          -- Re-raise other unique violations
          RAISE;
        END IF;
    END;
  END LOOP;
  
  -- Return the created request
  RETURN QUERY
  SELECT 
    cr.id,
    cr.request_number,
    cr.amount,
    cr.purpose,
    cr.description,
    cr.status,
    cr.created_at,
    cr.submitted_at
  FROM public.cash_requests cr
  WHERE cr.id = new_request_id;
  
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION create_cash_request TO authenticated;

-- Create RLS policy for the function
-- Note: The function runs with SECURITY DEFINER, so it bypasses RLS,
-- but we should still validate the user has permission to create requests
CREATE OR REPLACE FUNCTION validate_cashier_permission(p_cashier_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if the requesting user is the same as the cashier_id and has cashier role
  RETURN EXISTS (
    SELECT 1 
    FROM public.users 
    WHERE id = p_cashier_id 
    AND id = auth.uid() 
    AND role = 'cashier'
    AND is_active = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the create_cash_request function to include permission check
CREATE OR REPLACE FUNCTION create_cash_request(
  p_cashier_id UUID,
  p_amount DECIMAL(15,2),
  p_purpose TEXT,
  p_description TEXT DEFAULT NULL,
  p_status TEXT DEFAULT 'draft',
  p_submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS TABLE(
  id UUID,
  request_number TEXT,
  amount DECIMAL(15,2),
  purpose TEXT,
  description TEXT,
  status TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  submitted_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
  new_request_id UUID;
  new_request_number TEXT;
  retry_count INTEGER := 0;
  max_retries INTEGER := 5;
BEGIN
  -- Check permissions first
  IF NOT validate_cashier_permission(p_cashier_id) THEN
    RAISE EXCEPTION 'Permission denied. User must be an active cashier';
  END IF;

  -- Validate inputs
  IF p_amount <= 0 THEN
    RAISE EXCEPTION 'Amount must be greater than zero';
  END IF;
  
  IF p_purpose IS NULL OR trim(p_purpose) = '' THEN
    RAISE EXCEPTION 'Purpose is required';
  END IF;
  
  IF p_status NOT IN ('draft', 'submitted') THEN
    RAISE EXCEPTION 'Invalid status. Must be draft or submitted';
  END IF;

  -- Retry loop to handle race conditions
  WHILE retry_count < max_retries LOOP
    BEGIN
      -- Generate a new UUID for the request
      new_request_id := gen_random_uuid();
      
      -- Generate request number
      new_request_number := generate_request_number();
      
      -- Try to insert the request
      INSERT INTO public.cash_requests (
        id,
        cashier_id,
        request_number,
        amount,
        purpose,
        description,
        status,
        submitted_at
      ) VALUES (
        new_request_id,
        p_cashier_id,
        new_request_number,
        p_amount,
        trim(p_purpose),
        CASE WHEN p_description IS NOT NULL AND trim(p_description) != '' THEN trim(p_description) ELSE NULL END,
        p_status,
        p_submitted_at
      );
      
      -- If we get here, the insert was successful
      EXIT;
      
    EXCEPTION
      WHEN unique_violation THEN
        -- Check if it's the request_number constraint
        IF SQLERRM LIKE '%cash_requests_request_number_key%' THEN
          retry_count := retry_count + 1;
          IF retry_count >= max_retries THEN
            RAISE EXCEPTION 'Failed to generate unique request number after % attempts', max_retries;
          END IF;
          -- Small delay to reduce collision probability
          PERFORM pg_sleep(random() * 0.1);
        ELSE
          -- Re-raise other unique violations
          RAISE;
        END IF;
    END;
  END LOOP;
  
  -- Return the created request
  RETURN QUERY
  SELECT 
    cr.id,
    cr.request_number,
    cr.amount,
    cr.purpose,
    cr.description,
    cr.status,
    cr.created_at,
    cr.submitted_at
  FROM public.cash_requests cr
  WHERE cr.id = new_request_id;
  
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
