-- Create operations table to track cash operations (receiving/giving money)

CREATE TABLE IF NOT EXISTS public.operations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cash_request_id UUID NOT NULL REFERENCES public.cash_requests(id),
  operation_type TEXT NOT NULL CHECK (operation_type IN ('receive', 'give')),
  amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
  operation_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  performed_by UUID NOT NULL REFERENCES public.users(id),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.operations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for operations table
CREATE POLICY "operations_select_for_related_users" ON public.operations 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.cash_requests cr
      WHERE cr.id = cash_request_id 
      AND (
        cr.cashier_id = auth.uid() OR
        EXISTS (
          SELECT 1 FROM public.users 
          WHERE id = auth.uid() 
          AND role IN ('director', 'deputy_director', 'accountant')
        )
      )
    )
  );

CREATE POLICY "operations_insert_for_cashiers" ON public.operations 
  FOR INSERT WITH CHECK (
    performed_by = auth.uid() AND
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() 
      AND role = 'cashier'
    )
  );
