-- Insert the admin user into the users table
-- This user was created in Supabase auth with ID: cb5427f8-e250-493a-bf98-ac2763aa6074

INSERT INTO public.users (id, email, full_name, role, is_active)
VALUES (
  'cb5427f8-e250-493a-bf98-ac2763aa6074',
  '<EMAIL>',
  'System Administrator',
  'director',
  true
)
ON CONFLICT (id) DO UPDATE SET
  full_name = EXCLUDED.full_name,
  role = EXCLUDED.role,
  is_active = EXCLUDED.is_active;
