-- Fix race condition in request number generation
-- This migration addresses the duplicate key constraint violation

-- Drop existing trigger first
DROP TRIGGER IF EXISTS set_cash_request_number ON public.cash_requests;

-- Drop existing function
DROP FUNCTION IF EXISTS set_request_number();
DROP FUNCTION IF EXISTS generate_request_number();

-- Drop sequence if it exists (we're using a different approach)
DROP SEQUENCE IF EXISTS request_number_seq;

-- Create completely race-condition-free function using advisory locks
CREATE OR REPLACE FUNCTION generate_request_number()
RETURNS TEXT AS $$
DECLARE
  year_part TEXT;
  sequence_num INTEGER;
  request_num TEXT;
  lock_key BIGINT;
BEGIN
  year_part := EXTRACT(YEAR FROM NOW())::TEXT;

  -- Create a unique lock key based on the year
  -- Using a hash of the year string to create a consistent lock key
  lock_key := ('x' || substr(md5(year_part), 1, 15))::bit(60)::bigint;

  -- Acquire advisory lock to ensure only one process generates numbers at a time
  PERFORM pg_advisory_lock(lock_key);

  BEGIN
    -- Get the next sequence number for this year atomically
    SELECT COALESCE(MAX(
      CASE
        WHEN request_number ~ ('^' || year_part || '-[0-9]+$')
        THEN CAST(SPLIT_PART(request_number, '-', 2) AS INTEGER)
        ELSE 0
      END
    ), 0) + 1
    INTO sequence_num
    FROM public.cash_requests;

    -- Format: YYYY-NNNN (e.g., 2024-0001)
    request_num := year_part || '-' || LPAD(sequence_num::TEXT, 4, '0');

    -- Release the advisory lock
    PERFORM pg_advisory_unlock(lock_key);

    RETURN request_num;

  EXCEPTION
    WHEN OTHERS THEN
      -- Make sure to release the lock even if an error occurs
      PERFORM pg_advisory_unlock(lock_key);
      RAISE;
  END;
END;
$$ LANGUAGE plpgsql;

-- Create function to auto-generate request number
CREATE OR REPLACE FUNCTION set_request_number()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.request_number IS NULL OR NEW.request_number = '' THEN
    NEW.request_number := generate_request_number();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate trigger to auto-generate request number
CREATE TRIGGER set_cash_request_number 
  BEFORE INSERT ON public.cash_requests 
  FOR EACH ROW EXECUTE FUNCTION set_request_number();

-- Initialize the sequence based on existing data
DO $$
DECLARE
  max_sequence INTEGER;
  current_year TEXT;
BEGIN
  current_year := EXTRACT(YEAR FROM NOW())::TEXT;
  
  -- Find the highest sequence number for the current year
  SELECT COALESCE(MAX(
    CASE 
      WHEN request_number ~ ('^' || current_year || '-[0-9]+$') 
      THEN CAST(SPLIT_PART(request_number, '-', 2) AS INTEGER)
      ELSE 0 
    END
  ), 0) INTO max_sequence
  FROM public.cash_requests;
  
  -- Set the sequence to start from the next number
  PERFORM setval('request_number_seq', max_sequence + 1, false);
END $$;
