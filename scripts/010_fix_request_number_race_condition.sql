-- Fix race condition in request number generation
-- This migration addresses the duplicate key constraint violation

-- Drop existing trigger first
DROP TRIGGER IF EXISTS set_cash_request_number ON public.cash_requests;

-- Drop existing function
DROP FUNCTION IF EXISTS set_request_number();
DROP FUNCTION IF EXISTS generate_request_number();

-- Create sequence for request numbers (reset yearly)
CREATE SEQUENCE IF NOT EXISTS request_number_seq;

-- Create improved function to generate request numbers using sequence
CREATE OR REPLACE FUNCTION generate_request_number()
RETURNS TEXT AS $$
DECLARE
  year_part TEXT;
  sequence_num INTEGER;
  request_num TEXT;
  current_year INTEGER;
  last_reset_year INTEGER;
BEGIN
  current_year := EXTRACT(YEAR FROM NOW())::INTEGER;
  year_part := current_year::TEXT;
  
  -- Check if we need to reset the sequence for a new year
  -- Get the last reset year from existing requests
  SELECT COALESCE(
    (SELECT EXTRACT(YEAR FROM MAX(created_at))::INTEGER 
     FROM public.cash_requests 
     WHERE request_number ~ ('^' || year_part || '-[0-9]+$')), 
    current_year - 1
  ) INTO last_reset_year;
  
  -- If it's a new year, reset the sequence
  IF last_reset_year < current_year THEN
    PERFORM setval('request_number_seq', 1, false);
  END IF;
  
  -- Get next sequence number (thread-safe)
  sequence_num := nextval('request_number_seq');
  
  -- Format: YYYY-NNNN (e.g., 2024-0001)
  request_num := year_part || '-' || LPAD(sequence_num::TEXT, 4, '0');
  
  -- Double-check for uniqueness (safety net)
  WHILE EXISTS (SELECT 1 FROM public.cash_requests WHERE request_number = request_num) LOOP
    sequence_num := nextval('request_number_seq');
    request_num := year_part || '-' || LPAD(sequence_num::TEXT, 4, '0');
  END LOOP;
  
  RETURN request_num;
END;
$$ LANGUAGE plpgsql;

-- Create function to auto-generate request number
CREATE OR REPLACE FUNCTION set_request_number()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.request_number IS NULL OR NEW.request_number = '' THEN
    NEW.request_number := generate_request_number();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate trigger to auto-generate request number
CREATE TRIGGER set_cash_request_number 
  BEFORE INSERT ON public.cash_requests 
  FOR EACH ROW EXECUTE FUNCTION set_request_number();

-- Initialize the sequence based on existing data
DO $$
DECLARE
  max_sequence INTEGER;
  current_year TEXT;
BEGIN
  current_year := EXTRACT(YEAR FROM NOW())::TEXT;
  
  -- Find the highest sequence number for the current year
  SELECT COALESCE(MAX(
    CASE 
      WHEN request_number ~ ('^' || current_year || '-[0-9]+$') 
      THEN CAST(SPLIT_PART(request_number, '-', 2) AS INTEGER)
      ELSE 0 
    END
  ), 0) INTO max_sequence
  FROM public.cash_requests;
  
  -- Set the sequence to start from the next number
  PERFORM setval('request_number_seq', max_sequence + 1, false);
END $$;
