-- Create workflow history table to track all status changes and approvals

CREATE TABLE IF NOT EXISTS public.workflow_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cash_request_id UUID NOT NULL REFERENCES public.cash_requests(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES public.users(id),
  action TEXT NOT NULL CHECK (
    action IN ('created', 'submitted', 'approved', 'rejected', 'completed')
  ),
  from_status TEXT,
  to_status TEXT NOT NULL,
  comments TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.workflow_history ENABLE ROW LEVEL SECURITY;

-- RLS Policies for workflow_history table
CREATE POLICY "workflow_history_select_for_related_users" ON public.workflow_history 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.cash_requests cr
      WHERE cr.id = cash_request_id 
      AND (
        cr.cashier_id = auth.uid() OR
        EXISTS (
          SELECT 1 FROM public.users 
          WHERE id = auth.uid() 
          AND role IN ('director', 'deputy_director', 'accountant')
        )
      )
    )
  );

CREATE POLICY "workflow_history_insert_for_authorized_users" ON public.workflow_history 
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM public.cash_requests cr
      WHERE cr.id = cash_request_id 
      AND (
        cr.cashier_id = auth.uid() OR
        EXISTS (
          SELECT 1 FROM public.users 
          WHERE id = auth.uid() 
          AND role IN ('director', 'deputy_director', 'accountant')
        )
      )
    )
  );
