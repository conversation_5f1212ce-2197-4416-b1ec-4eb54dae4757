-- Fix infinite recursion in RLS policies
-- Drop existing policies that cause recursion
DROP POLICY IF EXISTS "users_select_own" ON public.users;
DROP POLICY IF EXISTS "users_select_all_for_directors" ON public.users;
DROP POLICY IF EXISTS "users_insert_for_directors" ON public.users;
DROP POLICY IF EXISTS "users_update_for_directors" ON public.users;
DROP POLICY IF EXISTS "users_delete_for_directors" ON public.users;

-- Create a security definer function to check user roles
-- This prevents recursion by using SECURITY DEFINER
CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
RETURNS TEXT
LANGUAGE SQL
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT role FROM public.users WHERE id = user_id;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_user_role(UUID) TO authenticated;

-- Create new RLS policies without recursion
CREATE POLICY "users_select_own" ON public.users 
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "users_select_all_for_directors" ON public.users 
  FOR SELECT USING (
    public.get_user_role(auth.uid()) IN ('director', 'deputy_director')
  );

CREATE POLICY "users_insert_for_directors" ON public.users 
  FOR INSERT WITH CHECK (
    public.get_user_role(auth.uid()) IN ('director', 'deputy_director')
  );

CREATE POLICY "users_update_for_directors" ON public.users 
  FOR UPDATE USING (
    public.get_user_role(auth.uid()) IN ('director', 'deputy_director')
  );

CREATE POLICY "users_delete_for_directors" ON public.users 
  FOR DELETE USING (
    public.get_user_role(auth.uid()) IN ('director', 'deputy_director')
  );
