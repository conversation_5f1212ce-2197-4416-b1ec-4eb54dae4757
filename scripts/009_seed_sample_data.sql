-- Seed script for Arabic Cash Request System
-- This script adds sample data for testing all system functionality

-- Insert sample users with different roles
INSERT INTO users (id, email, full_name, role, department, phone, created_at, updated_at) VALUES
-- Cashiers
('********-1111-1111-1111-********1111', '<EMAIL>', 'أحمد محمد الكاشير', 'cashier', 'المالية', '+************', NOW(), NOW()),
('********-2222-2222-2222-********2222', '<EMAIL>', 'فاطمة علي الكاشير', 'cashier', 'المالية', '+************', NOW(), NOW()),

-- Accountants
('********-3333-3333-3333-********3333', '<EMAIL>', 'محمد سالم المحاسب', 'accountant', 'المحاسبة', '+************', NOW(), NOW()),
('********-4444-4444-4444-********4444', '<EMAIL>', 'نورا أحمد المحاسبة', 'accountant', 'المحاسبة', '+************', NOW(), NOW()),

-- Deputy Directors
('********-5555-5555-5555-********5555', '<EMAIL>', 'خالد عبدالله نائب المدير', 'deputy_director', 'الإدارة', '+************', NOW(), NOW()),
('********-6666-6666-6666-********6666', '<EMAIL>', 'سارة محمد نائب المدير', 'deputy_director', 'الإدارة', '+************', NOW(), NOW()),

-- Directors (in addition to the admin user)
('********-7777-7777-7777-********7777', '<EMAIL>', 'عبدالرحمن سعد المدير', 'director', 'الإدارة العليا', '+************', NOW(), NOW());

-- Insert sample cash requests with various statuses
INSERT INTO cash_requests (
    id, request_number, requester_id, amount, purpose, status, 
    priority, requested_at, created_at, updated_at
) VALUES
-- Pending requests
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'CR-2024-001', '********-1111-1111-1111-********1111', 5000.00, 'شراء مستلزمات مكتبية', 'pending', 'medium', NOW() - INTERVAL '2 hours', NOW() - INTERVAL '2 hours', NOW() - INTERVAL '2 hours'),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'CR-2024-002', '********-2222-2222-2222-********2222', 15000.00, 'دفع فواتير الكهرباء والماء', 'pending', 'high', NOW() - INTERVAL '1 hour', NOW() - INTERVAL '1 hour', NOW() - INTERVAL '1 hour'),

-- Approved by accountant, waiting for deputy director
('cccccccc-cccc-cccc-cccc-cccccccccccc', 'CR-2024-003', '********-1111-1111-1111-********1111', 8000.00, 'صيانة أجهزة الكمبيوتر', 'approved_by_accountant', 'medium', NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day', NOW() - INTERVAL '4 hours'),

-- Approved by deputy director, waiting for director
('dddddddd-dddd-dddd-dddd-dddddddddddd', 'CR-2024-004', '********-2222-2222-2222-********2222', 25000.00, 'شراء معدات جديدة للمكتب', 'approved_by_deputy', 'high', NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 hours'),

-- Fully approved, ready for cash operation
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'CR-2024-005', '********-1111-1111-1111-********1111', 3000.00, 'مصاريف سفر موظف', 'approved', 'low', NOW() - INTERVAL '3 days', NOW() - INTERVAL '3 days', NOW() - INTERVAL '1 hour'),

-- Completed requests
('ffffffff-ffff-ffff-ffff-ffffffffffff', 'CR-2024-006', '********-2222-2222-2222-********2222', 12000.00, 'دفع رواتب العمال', 'completed', 'high', NOW() - INTERVAL '5 days', NOW() - INTERVAL '5 days', NOW() - INTERVAL '1 day'),

-- Rejected request
('gggggggg-gggg-gggg-gggg-gggggggggggg', 'CR-2024-007', '********-1111-1111-1111-********1111', 50000.00, 'شراء سيارة للشركة', 'rejected', 'low', NOW() - INTERVAL '4 days', NOW() - INTERVAL '4 days', NOW() - INTERVAL '3 days');

-- Insert workflow history for the requests
INSERT INTO workflow_history (
    id, request_id, status, actor_id, action, comments, created_at
) VALUES
-- History for CR-2024-003 (approved by accountant)
('h1111111-1111-1111-1111-********1111', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 'pending', '********-1111-1111-1111-********1111', 'created', 'طلب جديد لصيانة أجهزة الكمبيوتر', NOW() - INTERVAL '1 day'),
('h2222222-2222-2222-2222-********2222', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 'approved_by_accountant', '********-3333-3333-3333-********3333', 'approved', 'تمت الموافقة من قبل المحاسب - المبلغ مناسب', NOW() - INTERVAL '4 hours'),

-- History for CR-2024-004 (approved by deputy)
('h3333333-3333-3333-3333-********3333', 'dddddddd-dddd-dddd-dddd-dddddddddddd', 'pending', '********-2222-2222-2222-********2222', 'created', 'طلب شراء معدات جديدة للمكتب', NOW() - INTERVAL '2 days'),
('h4444444-4444-4444-4444-********4444', 'dddddddd-dddd-dddd-dddd-dddddddddddd', 'approved_by_accountant', '********-4444-4444-4444-********4444', 'approved', 'تمت الموافقة من قبل المحاسب', NOW() - INTERVAL '1 day'),
('h5555555-5555-5555-5555-********5555', 'dddddddd-dddd-dddd-dddd-dddddddddddd', 'approved_by_deputy', '********-5555-5555-5555-********5555', 'approved', 'تمت الموافقة من قبل نائب المدير - معدات ضرورية', NOW() - INTERVAL '2 hours'),

-- History for CR-2024-005 (fully approved)
('h6666666-6666-6666-6666-********6666', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'pending', '********-1111-1111-1111-********1111', 'created', 'طلب مصاريف سفر موظف', NOW() - INTERVAL '3 days'),
('h7777777-7777-7777-7777-********7777', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'approved_by_accountant', '********-3333-3333-3333-********3333', 'approved', 'تمت الموافقة من قبل المحاسب', NOW() - INTERVAL '2 days'),
('h8888888-8888-8888-8888-************', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'approved_by_deputy', '********-6666-6666-6666-********6666', 'approved', 'تمت الموافقة من قبل نائب المدير', NOW() - INTERVAL '1 day'),
('h9999999-9999-9999-9999-************', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'approved', 'cb5427f8-e250-493a-bf98-ac2763aa6074', 'approved', 'تمت الموافقة النهائية من قبل المدير', NOW() - INTERVAL '1 hour'),

-- History for CR-2024-006 (completed)
('ha111111-1111-1111-1111-********1111', 'ffffffff-ffff-ffff-ffff-ffffffffffff', 'pending', '********-2222-2222-2222-********2222', 'created', 'طلب دفع رواتب العمال', NOW() - INTERVAL '5 days'),
('ha222222-2222-2222-2222-********2222', 'ffffffff-ffff-ffff-ffff-ffffffffffff', 'approved_by_accountant', '********-4444-4444-4444-********4444', 'approved', 'تمت الموافقة من قبل المحاسب', NOW() - INTERVAL '4 days'),
('ha333333-3333-3333-3333-********3333', 'ffffffff-ffff-ffff-ffff-ffffffffffff', 'approved_by_deputy', '********-5555-5555-5555-********5555', 'approved', 'تمت الموافقة من قبل نائب المدير', NOW() - INTERVAL '3 days'),
('ha444444-4444-4444-4444-********4444', 'ffffffff-ffff-ffff-ffff-ffffffffffff', 'approved', '********-7777-7777-7777-********7777', 'approved', 'تمت الموافقة النهائية من قبل المدير', NOW() - INTERVAL '2 days'),
('ha555555-5555-5555-5555-********5555', 'ffffffff-ffff-ffff-ffff-ffffffffffff', 'completed', '********-2222-2222-2222-********2222', 'completed', 'تم صرف المبلغ بنجاح', NOW() - INTERVAL '1 day'),

-- History for CR-2024-007 (rejected)
('hb111111-1111-1111-1111-********1111', 'gggggggg-gggg-gggg-gggg-gggggggggggg', 'pending', '********-1111-1111-1111-********1111', 'created', 'طلب شراء سيارة للشركة', NOW() - INTERVAL '4 days'),
('hb222222-2222-2222-2222-********2222', 'gggggggg-gggg-gggg-gggg-gggggggggggg', 'rejected', '********-3333-3333-3333-********3333', 'rejected', 'تم رفض الطلب - المبلغ كبير جداً ويحتاج موافقة مجلس الإدارة', NOW() - INTERVAL '3 days');

-- Insert sample operations (cash transactions)
INSERT INTO operations (
    id, request_id, operator_id, operation_type, amount, 
    recipient_name, recipient_id, notes, created_at
) VALUES
-- Cash given for completed request
('op111111-1111-1111-1111-********1111', 'ffffffff-ffff-ffff-ffff-ffffffffffff', '********-2222-2222-2222-********2222', 'give_money', 12000.00, 'محمد أحمد العامل', '12345678901', 'تم صرف رواتب العمال لشهر ديسمبر', NOW() - INTERVAL '1 day'),

-- Cash received (return or deposit)
('op222222-2222-2222-2222-********2222', NULL, '********-1111-1111-1111-********1111', 'receive_money', 2000.00, 'سارة محمد الموظفة', '98765432109', 'إرجاع مبلغ زائد من مصاريف السفر', NOW() - INTERVAL '3 hours');

-- Update request numbers sequence
SELECT setval('cash_requests_request_number_seq', 7);

COMMIT;
