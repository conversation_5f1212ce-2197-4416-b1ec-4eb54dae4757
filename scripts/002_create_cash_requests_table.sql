-- Create cash requests table
-- Status: draft, submitted, director_approved, accountant_approved, completed, rejected

CREATE TABLE IF NOT EXISTS public.cash_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cashier_id UUID NOT NULL REFERENCES public.users(id),
  request_number TEXT NOT NULL UNIQUE,
  amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
  purpose TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'draft' CHECK (
    status IN ('draft', 'submitted', 'director_approved', 'accountant_approved', 'completed', 'rejected')
  ),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  submitted_at TIMESTAMP WITH TIME ZONE,
  director_approved_at TIMESTAMP WITH TIME ZONE,
  accountant_approved_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  rejected_at TIMESTAMP WITH TIME ZONE,
  rejection_reason TEXT
);

-- Enable RLS
ALTER TABLE public.cash_requests ENABLE ROW LEVEL SECURITY;

-- RLS Policies for cash_requests table
CREATE POLICY "cash_requests_select_own_for_cashiers" ON public.cash_requests 
  FOR SELECT USING (
    cashier_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() 
      AND role IN ('director', 'deputy_director', 'accountant')
    )
  );

CREATE POLICY "cash_requests_insert_for_cashiers" ON public.cash_requests 
  FOR INSERT WITH CHECK (
    cashier_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() 
      AND role = 'cashier'
    )
  );

CREATE POLICY "cash_requests_update_for_cashiers" ON public.cash_requests 
  FOR UPDATE USING (
    (cashier_id = auth.uid() AND status IN ('draft', 'rejected')) OR
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() 
      AND role IN ('director', 'deputy_director', 'accountant')
    )
  );

CREATE POLICY "cash_requests_delete_for_cashiers" ON public.cash_requests 
  FOR DELETE USING (
    cashier_id = auth.uid() AND status = 'draft'
  );
