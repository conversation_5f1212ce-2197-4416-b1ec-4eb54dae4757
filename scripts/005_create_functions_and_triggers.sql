-- <PERSON>reate function to generate request numbers
CREATE OR REPLACE FUNCTION generate_request_number()
RETURNS TEXT AS $$
DECLARE
  year_part TEXT;
  sequence_num INTEGER;
  request_num TEXT;
BEGIN
  year_part := EXTRACT(YEAR FROM NOW())::TEXT;
  
  -- Get the next sequence number for this year
  SELECT COALESCE(MAX(
    CASE 
      WHEN request_number ~ ('^' || year_part || '-[0-9]+$') 
      THEN CAST(SPLIT_PART(request_number, '-', 2) AS INTEGER)
      ELSE 0 
    END
  ), 0) + 1
  INTO sequence_num
  FROM public.cash_requests;
  
  request_num := year_part || '-' || LPAD(sequence_num::TEXT, 4, '0');
  
  RETURN request_num;
END;
$$ LANGUAGE plpgsql;

-- <PERSON>reate function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON>reate triggers for updated_at columns
CREATE TRIGGER update_users_updated_at 
  BEFORE UPDATE ON public.users 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cash_requests_updated_at 
  BEFORE UPDATE ON public.cash_requests 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to auto-generate request number
CREATE OR REPLACE FUNCTION set_request_number()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.request_number IS NULL OR NEW.request_number = '' THEN
    NEW.request_number := generate_request_number();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-generate request number
CREATE TRIGGER set_cash_request_number 
  BEFORE INSERT ON public.cash_requests 
  FOR EACH ROW EXECUTE FUNCTION set_request_number();
