-- Create function to generate request numbers using advisory locks (completely race-condition free)
CREATE OR REPLACE FUNCTION generate_request_number()
RETURNS TEXT AS $$
DECLARE
  year_part TEXT;
  sequence_num INTEGER;
  request_num TEXT;
  lock_key BIGINT;
BEGIN
  year_part := EXTRACT(YEAR FROM NOW())::TEXT;

  -- Create a unique lock key based on the year
  -- Using a hash of the year string to create a consistent lock key
  lock_key := ('x' || substr(md5(year_part), 1, 15))::bit(60)::bigint;

  -- Acquire advisory lock to ensure only one process generates numbers at a time
  PERFORM pg_advisory_lock(lock_key);

  BEGIN
    -- Get the next sequence number for this year atomically
    SELECT COALESCE(MAX(
      CASE
        WHEN request_number ~ ('^' || year_part || '-[0-9]+$')
        THEN CAST(SPLIT_PART(request_number, '-', 2) AS INTEGER)
        ELSE 0
      END
    ), 0) + 1
    INTO sequence_num
    FROM public.cash_requests;

    -- Format: YYYY-NNNN (e.g., 2024-0001)
    request_num := year_part || '-' || LPAD(sequence_num::TEXT, 4, '0');

    -- Release the advisory lock
    PERFORM pg_advisory_unlock(lock_key);

    RETURN request_num;

  EXCEPTION
    WHEN OTHERS THEN
      -- Make sure to release the lock even if an error occurs
      PERFORM pg_advisory_unlock(lock_key);
      RAISE;
  END;
END;
$$ LANGUAGE plpgsql;

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at columns
CREATE TRIGGER update_users_updated_at 
  BEFORE UPDATE ON public.users 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cash_requests_updated_at 
  BEFORE UPDATE ON public.cash_requests 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to auto-generate request number
CREATE OR REPLACE FUNCTION set_request_number()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.request_number IS NULL OR NEW.request_number = '' THEN
    NEW.request_number := generate_request_number();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-generate request number
CREATE TRIGGER set_cash_request_number 
  BEFORE INSERT ON public.cash_requests 
  FOR EACH ROW EXECUTE FUNCTION set_request_number();
